using System;

namespace MaoYouJi
{
  [MessageLocationHand<PERSON>(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class NpcBaseMsgHandler : MessageLocationHandler<MapNode, ServerNpcTalkListReq, ServerNpcTalkListResp>
  {
    protected override async ETTask Run(MapNode nowMap, ServerNpcTalkListReq request, ServerNpcTalkListResp response)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      if (!nowMap.ContainsNpc(request.NpcName))
      {
        response.SetError("NPC不在当前地图");
        return;
      }
      ServerNpcProcStruct serverNpcProcStruct = new ServerNpcProcStruct
      {
        ServerNpcTalkListResp = response,
        User = user,
        MapNode = nowMap
      };
      if (EventSystem.Instance.TryGetInvoke<ServerNpcProcStruct, ETTask>((long)request.NpcName, out var invokeHandler))
      {
        try
        {
          await invokeHandler.Handle(serverNpcProcStruct);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
          response.SetError("服务器内部错误");
        }
      }
      else
      {
        response.SetError("服务器内部错误");
      }
    }
  }
}
