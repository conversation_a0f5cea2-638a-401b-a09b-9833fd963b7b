namespace MaoYouJi
{
  [Invoke((long)NpcTalkOptionEnum.get_double_exp)]
  public class GetDoubleExpHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.level > 20)
      {
        user.SendToast("您的等级大于20级");
        return;
      }
      UserStateDetail userStateDetail = new UserStateDetail();
      userStateDetail.userState = UserStateEnum.ADD_EXP;
      userStateDetail.val = new long[] { 2 };
      userStateDetail.existTime = 60 * 1000 * 60;
      userStateDetail.endTime = TimeInfo.Instance.ServerNow() + userStateDetail.existTime;
      user.AddUserStatus(userStateDetail);
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.talkList = "show_double_exp_time";
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.GeDuo_NewPlayer)]
  public class GeDuoNewPlayerHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      UserGetInfoCnt userGetInfoCnt = user.GetComponent<UserGetInfoCnt>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      if (userGetInfoCnt.xinShouLiHeCnt >= 4)
      {
        outParams.talkList = "no_more_gift";
        user.SendMessage(outParams);
        return;
      }

      if (attackComponent.level < 10 * (userGetInfoCnt.xinShouLiHeCnt + 1))
      {
        outParams.talkList = "not_enough_level";
        outParams.talkParamsMap["targetLevel"] = (10 * (userGetInfoCnt.xinShouLiHeCnt + 1)).ToString();
        user.SendMessage(outParams);
        return;
      }

      userGetInfoCnt.xinShouLiHeCnt++;
      ThingNameEnum thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv10;
      if (userGetInfoCnt.xinShouLiHeCnt == 1)
      {
        thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv10;
      }
      else if (userGetInfoCnt.xinShouLiHeCnt == 2)
      {
        thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv20;
      }
      else if (userGetInfoCnt.xinShouLiHeCnt == 3)
      {
        thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv30;
      }
      else if (userGetInfoCnt.xinShouLiHeCnt == 4)
      {
        thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv40;
      }

      ThingGiveInfo giveInfo = new ThingGiveInfo(thingNameEnum, OwnType.PRIVATE, 1);
      Thing thing = bagComponent.GiveThing(giveInfo);
      outParams.talkList = "get_gift_success";
      outParams.talkParamsMap["giftName"] = thing.name;
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.exchange_phoenix_egg)]
  public class ExchangePhoenixEggHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      Thing huoyuanSuEgg = bagComponent.GetThingInBag<Thing>(ThingNameEnum.HuoYuanSu_Egg);
      Thing fengYuanSuEgg = bagComponent.GetThingInBag<Thing>(ThingNameEnum.FengYuanSu_Egg);
      Thing diYuanSuEgg = bagComponent.GetThingInBag<Thing>(ThingNameEnum.DiYuanSu_Egg);
      Thing shuiYuanSuEgg = bagComponent.GetThingInBag<Thing>(ThingNameEnum.ShuiYuanSu_Egg);

      if (huoyuanSuEgg == null || fengYuanSuEgg == null || diYuanSuEgg == null || shuiYuanSuEgg == null)
      {
        outParams.talkList = "no_enough_egg";
        user.SendMessage(outParams);
        return;
      }

      bagComponent.AddThingNumWithSend(shuiYuanSuEgg, -1);
      bagComponent.AddThingNumWithSend(huoyuanSuEgg, -1);
      bagComponent.AddThingNumWithSend(fengYuanSuEgg, -1);
      bagComponent.AddThingNumWithSend(diYuanSuEgg, -1);

      ThingGiveInfo giveInfo = new ThingGiveInfo(ThingNameEnum.FengHuang_Egg, OwnType.SHARE, 1);
      bagComponent.GiveThing(giveInfo);
      outParams.talkList = "exchange_success";
      user.SendMessage(outParams);

      ChatProSystem.SendGlobalNotice("恭喜" + user.nickname + "集齐了四大元素成功兑换了猫游大陆神宠凤凰蛋！", ChatType.Sys_Chat);
      await ETTask.CompletedTask;
    }
  }
}