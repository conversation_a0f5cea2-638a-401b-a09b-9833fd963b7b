namespace MaoYouJi
{
@TalkOption(NpcTalkOptionEnum.get_double_exp)
  public QueryNpcTalkOptionsOut getDoubleExp(QueryNpcTalkOptionsIn inParams) throws Exception {
    UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
    User user = userCacheInfo.user;
    if (user.nowAttack.level > 20) {
      throw new MyWarn("您的等级大于20级");
    }
    UserStateDetail userStateDetail = new UserStateDetail();
    userStateDetail.userState = UserStateEnum.ADD_EXP;
    userStateDetail.val = new long[] { 2 };
    userStateDetail.existTime = 60 * 1000 * 60;
    userStateDetail.schedulerId = UserSystem.getUserStateKey(user, UserStateEnum.ADD_EXP);
    userStateDetail.endTime = System.currentTimeMillis() + userStateDetail.existTime;
    userBaseProc.addUserState(user, userStateDetail);
    QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
    outParams.talkList = "show_double_exp_time";
    return outParams;
  }

  @TalkOption(NpcTalkOptionEnum.GeDuo_NewPlayer)
  public QueryNpcTalkOptionsOut getNewPlayer(QueryNpcTalkOptionsIn inParams) throws Exception {
    UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
    UserGetInfoCnt userGetInfoCnt = userCacheInfo.userSystem.userCountInfo.userGetInfoCnt;
    QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
    if (userGetInfoCnt.xinShouLiHeCnt >= 4) {
      outParams.talkList = "no_more_gift";
      return outParams;
    }
    User user = userCacheInfo.user;
    if (user.nowAttack.level < 10 * (userGetInfoCnt.xinShouLiHeCnt + 1)) {
      outParams.talkList = "not_enough_level";
      outParams.talkParamsMap.put("targetLevel", String.valueOf(10 * (userGetInfoCnt.xinShouLiHeCnt + 1)));
      return outParams;
    }
    userGetInfoCnt.xinShouLiHeCnt++;
    ThingNameEnum thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv10;
    if (userGetInfoCnt.xinShouLiHeCnt == 1) {
      thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv10;
    } else if (userGetInfoCnt.xinShouLiHeCnt == 2) {
      thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv20;
    } else if (userGetInfoCnt.xinShouLiHeCnt == 3) {
      thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv30;
    } else if (userGetInfoCnt.xinShouLiHeCnt == 4) {
      thingNameEnum = ThingNameEnum.XinShou_LiHe_Lv40;
    }
    Thing.ThingGiveInfo giveInfo = new Thing.ThingGiveInfo(thingNameEnum, OwnType.PRIVATE, 1);
    bagProc.giveThing(userCacheInfo.bagSystem, giveInfo);
    outParams.talkList = "get_gift_success";
    outParams.talkParamsMap.put("giftName", thingNameEnum.getName());
    return outParams;
  }

  @TalkOption(NpcTalkOptionEnum.exchange_phoenix_egg)
  public QueryNpcTalkOptionsOut exchangePhoenixEgg(QueryNpcTalkOptionsIn inParams) throws Exception {
    UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
    User user = userCacheInfo.user;
    BagSystem bagSystem = userCacheInfo.bagSystem;
    QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
    Thing huoyuanSuEgg = bagSystem.GetThingInBag(ThingNameEnum.HuoYuanSu_Egg, null, null),
        fengYuanSuEgg = bagSystem.GetThingInBag(ThingNameEnum.FengYuanSu_Egg, null, null),
        diYuanSuEgg = bagSystem.GetThingInBag(ThingNameEnum.DiYuanSu_Egg, null, null),
        shuiYuanSuEgg = bagSystem.GetThingInBag(ThingNameEnum.ShuiYuanSu_Egg, null, null);
    if (huoyuanSuEgg == null || fengYuanSuEgg == null || diYuanSuEgg == null || shuiYuanSuEgg == null) {
      outParams.talkList = "no_enough_egg";
      return outParams;
    }
    bagSystem.addThingNumWithSend(shuiYuanSuEgg, -1);
    bagSystem.addThingNumWithSend(huoyuanSuEgg, -1);
    bagSystem.addThingNumWithSend(fengYuanSuEgg, -1);
    bagSystem.addThingNumWithSend(diYuanSuEgg, -1);
    ThingGiveInfo giveInfo = new ThingGiveInfo(ThingNameEnum.FengHuang_Egg, OwnType.SHARE, 1);
    bagProc.giveThing(bagSystem, giveInfo);
    outParams.talkList = "exchange_success";
    chatProc.sendGlobalNotice("恭喜" + user.name + "集齐了四大元素成功兑换了猫游大陆神宠凤凰蛋！", ChatType.Sys_Chat);
    return outParams;
  }
}