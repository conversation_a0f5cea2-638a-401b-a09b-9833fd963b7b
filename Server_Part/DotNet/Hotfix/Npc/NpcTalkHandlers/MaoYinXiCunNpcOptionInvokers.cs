namespace MaoYouJi
{
  [Invoke((long)NpcTalkOptionEnum.try_to_use_da_pao)]
  public class TryToUseDaPaoHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      MapNode currentMapNode = data.MapNode;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();

      if (attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        user.SendToast("当前状态无法使用大炮");
        return;
      }

      // 发送第一条消息到当前地图
      currentMapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = user.nickname + "战战兢兢的摸了一下大炮，脚下突然出现了一个黑洞，" + user.nickname + "还来不急反应，已经被一片黑暗包围了!",
        chatType = ChatType.Local_Chat
      });
      MapNode targetMapNode = GlobalInfoCache.Instance.GetMapNode(MapNameConstant.TuoBaCheng, "中心广场");
      // 移动用户到拖把城中心广场
      if (targetMapNode != null)
      {
        LogicRet moveResult = moveComponent.MoveTo(targetMapNode);
        if (moveResult.IsSuccess)
        {
          // 发送第二条消息到目标地图
          targetMapNode.SendMessageToMapUser(new ServerSendChatMsg
          {
            content = "一阵惊恐的叫声由远而近，只见" + user.nickname + "紧闭双眼，脸色发白，从空中悠悠飘下。脚都落了地，还一副不知道发生了什么事的样子。",
            chatType = ChatType.Local_Chat
          });
        }
      }
      await ETTask.CompletedTask;
    }
  }
}