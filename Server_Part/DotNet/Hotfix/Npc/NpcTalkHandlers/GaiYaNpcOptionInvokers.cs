namespace MaoYouJi
{
  @TalkOption(NpcTalkOptionEnum.server_enter_magic_space)
  public CommonOutParams serverEnterMagicSpace(QueryNpcTalkOptionsIn inParams) throws Exception
  {
    UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
    User user = userCacheInfo.user;
    if (!user.nowMap.equals("盖亚之城") || !user.nowPoint.equals("魔导师学院")) {
      return new ShowToastOut("您的位置不正确，无法进入魔法空间");
    }
    mapProc.changeUserPoint(user, "魔法空间", "魔法空间");
    return new QueryNpcTalkOptionsOut("close");
}

@TalkOption(NpcTalkOptionEnum.server_quit_magic_space)
  public CommonOutParams serverQuitMagicSpace(QueryNpcTalkOptionsIn inParams) throws Exception
{
  UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
  User user = userCacheInfo.user;
    if (!user.nowMap.equals("魔法空间") || !user.nowPoint.equals("魔法空间")) {
    return new ShowToastOut("您的位置不正确，无法退出魔法空间");
  }
  mapProc.changeUserPoint(user, "盖亚之城", "魔导师学院");
    return new QueryNpcTalkOptionsOut("close");
  }
}