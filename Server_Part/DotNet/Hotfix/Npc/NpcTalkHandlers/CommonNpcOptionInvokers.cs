namespace MaoYouJi
{
  [Invoke((long)NpcTalkOptionEnum.query_liangongfang_time)]
  public class QueryLiangGongFangTimeHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.talkParamsMap["liangongfang_time"] = vipFuncInfo.remainLianGongFangTime.ToString();
      outParams.talkParamsMap["gaoji_liangongfang_time"] = vipFuncInfo.remainGaoJiLianGongFangTime.ToString();
      outParams.talkList = "show_liangongfang_time";
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.query_lvguan_time)]
  public class QueryLvGuanTimeHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.talkParamsMap["lvguan_time"] = vipFuncInfo.remainLvGuanTime.ToString();
      outParams.talkParamsMap["gaoji_lvguan_time"] = vipFuncInfo.remainGaoJiLvGuanTime.ToString();
      outParams.talkList = "show_lvguan_time";
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.get_repair_params)]
  public class GetRepairParamsHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.openDialogId = 1014; // 修理对话框ID
      foreach (Equipment equip in equipComponent.equipList)
      {
        if (equip == null || equip.thingType != ThingType.EQUIP || EquipConstant.IsSpecialEquip(equip.thingSubType))
        {
          continue;
        }
        if (equip.useCnt == equip.remainUseCnt)
        {
          continue;
        }
        outParams.talkParamsMap[equip.thingId.ToString()] = equip.GetRepairCost().ToString();
      }
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.leave_xiaoheiwu)]
  public class LeaveXiaoHeiWuHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      User user = data.User;
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      if (user.evilNum > 0)
      {
        outParams.talkList = "cannot_leave_xiaoheiwu";
        outParams.talkParamsMap["evil_num"] = user.evilNum.ToString();
        user.SendMessage(outParams);
      }
      else
      {
        MapNode newNode = GlobalInfoCache.Instance.GetMapNode(MapNameConstant.TuoBaCheng, "中心广场");
        if (newNode != null)
        {
          MoveComponent moveComponent = user.GetComponent<MoveComponent>();
          moveComponent.MoveTo(newNode);
          newNode.SendMessagesToMapUser(new ServerSendChatMsg()
          {
            chatType = ChatType.Local_Chat,
            content = "<u>" + user.nickname + "</u>在经历了漫长的小黑屋惩罚以后，终于重获了自由，他向小黑屋管理员挥了挥手，并仰天长笑道：我辈岂能苟活于此！然后潇洒地走了出去。",
          });
        }
        outParams.talkList = "close";
        user.SendMessage(outParams);
      }
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.submit_hunpo_jinghua)]
  public class SubmitHunpoJinghuaHandler : AInvokeHandler<NpcOptionProcStruct, ETTask>
  {
    public override async ETTask Handle(NpcOptionProcStruct data)
    {
      User user = data.User;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      RelationComponent relationComponent = user.GetComponent<RelationComponent>();
      int idx = data.ClientNpcOptionMsg.idx;
      ThingNameEnum? needHunPoName = null;
      int addExp = 0;
      RelationTypeEnum? relationType = null;
      if (idx == 0)
      {
        // 怨灵魂魄精华，增加猫隐村关系200点
        needHunPoName = ThingNameEnum.YuanLing_HunPo_JingHua;
        addExp = 200;
        relationType = RelationTypeEnum.MaoYinCun_Relation;
      }
      else if (idx == 1)
      {
        // 怨灵魂魄精华，增加拖把城关系150点
        needHunPoName = ThingNameEnum.YuanLing_HunPo_JingHua;
        addExp = 150;
        relationType = RelationTypeEnum.TuoBaCheng_Relation;
      }
      else if (idx == 2)
      {
        // 死灵魂魄精华，增加雪原城关系200点
        needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
        addExp = 200;
        relationType = RelationTypeEnum.XueYuanCheng_Relation;
      }
      else if (idx == 3)
      {
        // 死灵魂魄精华，增加冰封城关系180点
        needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
        addExp = 180;
        relationType = RelationTypeEnum.BingFengCheng_Relation;
      }
      else if (idx == 4)
      {
        // 死灵魂魄精华，增加白马港关系150点
        needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
        addExp = 150;
        relationType = RelationTypeEnum.BaiMaGang_Relation;
      }
      else if (idx == 5)
      {
        // 魁鬼魂魄精华，增加西赛平原关系200点
        needHunPoName = ThingNameEnum.KuiGui_HunPo_JingHua;
        addExp = 200;
        relationType = RelationTypeEnum.XiSaiPingYuan_Relation;
      }
      else if (idx == 6)
      {
        // 魁鬼魂魄精华，增加精灵世界关系150点
        needHunPoName = ThingNameEnum.KuiGui_HunPo_JingHua;
        addExp = 150;
        relationType = RelationTypeEnum.JingLingShiJie_Relation;
      }
      else if (idx == 7)
      {
        // 魔王魂魄精华，增加盖亚之城关系200点
        needHunPoName = ThingNameEnum.MoWang_HunPo_JingHua;
        addExp = 200;
        relationType = RelationTypeEnum.GaiYaZhiCheng_Relation;
      }
      if (needHunPoName == null || relationType == null)
      {
        user.SendToast("服务器错误，请反馈给管理员！");
        return;
      }
      Thing hunPoJingHua = bagComponent.GetThingInBag<Thing>(needHunPoName.Value);
      if (hunPoJingHua == null || hunPoJingHua.num < 1)
      {
        user.SendToast("背包中没有足够的" + EnumDescriptionCache.GetDescription(needHunPoName.Value) + "！");
        return;
      }
      bagComponent.AddThingNumWithSend(hunPoJingHua, -1);
      relationComponent.AddRelationExp(relationType.Value, addExp);
      user.SendToast("您的" + EnumDescriptionCache.GetDescription(relationType.Value) + "关系增加了" + addExp + "点！");
      await ETTask.CompletedTask;
    }
  }
}