using System.Threading.Tasks;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  namespace <PERSON>YouJi
  {
    [Invoke((long)NpcTalkOptionEnum.query_liangongfang_time)]
    public class MuJinJinHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
    {
      public override async ETTask Handle(ServerNpcProcStruct data)
      {
        User user = data.User;
        UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
        ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
        outParams.talkParamsMap["liangongfang_time"] = vipFuncInfo.remainLianGongFangTime.ToString();
        outParams.talkParamsMap["gaoji_liangongfang_time"] = vipFuncInfo.remainGaoJiLianGongFangTime.ToString();
        outParams.talkList = "show_liangongfang_time";
        await ETTask.CompletedTask;
      }
    }

    @TalkOption(NpcTalkOptionEnum.query_lvguan_time)
  public QueryNpcTalkOptionsOut queryLvGuanTime(QueryNpcTalkOptionsIn inParams)
      {
        UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
        UserCountInfo userCountInfo = userCacheInfo.userSystem.userCountInfo;
        QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
        outParams.talkParamsMap.put("lvguan_time", String.valueOf(userCountInfo.vipFuncInfo.remainLvGuanTime));
        outParams.talkParamsMap.put("gaoji_lvguan_time", String.valueOf(userCountInfo.vipFuncInfo.remainGaoJiLvGuanTime));
        outParams.talkList = "show_lvguan_time";
        return outParams;
      }

      @TalkOption(NpcTalkOptionEnum.get_repair_params)
  public QueryNpcTalkOptionsOut getRepairParams(QueryNpcTalkOptionsIn inParams)
      {
        UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
        User user = userCacheInfo.user;
        QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
        outParams.openDialogId = 1014; // 修理对话框ID
        for (Equipment equip : user.nowAttack.equipList)
        {
          if (equip.thingType != ThingType.EQUIP || Equipment.specialEquipList.contains(equip.thingSubType))
          {
            continue;
          }
          if (equip.useCnt == equip.remainUseCnt)
          {
            continue;
          }
          outParams.talkParamsMap.put(equip.thingId.toString(), String.valueOf(EquipProc.getRepairCost(equip)));
        }
        return outParams;
      }

      @TalkOption(NpcTalkOptionEnum.leave_xiaoheiwu)
  public QueryNpcTalkOptionsOut leaveXiaoHeiWu(QueryNpcTalkOptionsIn inParams)
      {
        UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
        User user = userCacheInfo.user;
        QueryNpcTalkOptionsOut outParams = new QueryNpcTalkOptionsOut();
        if (user.evilNum > 0)
        {
          outParams.talkList = "cannot_leave_xiaoheiwu";
          outParams.talkParamsMap.put("evil_num", String.valueOf(user.evilNum));
          return outParams;
        }
        else
        {
          MapNode oldNode = ThreadUtil.getMapNode();
          MapNode newNode = mapProc.getMapNode("拖把城", "中心广场");
          mapProc.changeUserPoint(user, oldNode, newNode);
          chatProc.sendMessageToMapUsers(
              new SendChatOut("<u>" + user.name + "</u>在经历了漫长的小黑屋惩罚以后，终于重获了自由，他向小黑屋管理员挥了挥手，并仰天长笑道：我辈岂能苟活于此！然后潇洒地走了出去。",
                  ChatType.Local_Chat),
              newNode);
          outParams.talkList = "close";
          return outParams;
        }
      }

      @TalkOption(NpcTalkOptionEnum.submit_hunpo_jinghua)
  public CommonOutParams submitHunpoJinghua(QueryNpcTalkOptionsIn inParams)
      {
        UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
        BagSystem bagSystem = userCacheInfo.bagSystem;
        User user = userCacheInfo.user;
        int idx = inParams.idx;
        ThingNameEnum needHunPoName = null;
        int addExp = 0;
        RelationTypeEnum relationType = null;
        if (idx == 0)
        {
          // 怨灵魂魄精华，增加猫隐村关系200点
          needHunPoName = ThingNameEnum.YuanLing_HunPo_JingHua;
          addExp = 200;
          relationType = RelationTypeEnum.MaoYinCun_Relation;
        }
        else if (idx == 1)
        {
          // 怨灵魂魄精华，增加拖把城关系150点
          needHunPoName = ThingNameEnum.YuanLing_HunPo_JingHua;
          addExp = 150;
          relationType = RelationTypeEnum.TuoBaCheng_Relation;
        }
        else if (idx == 2)
        {
          // 死灵魂魄精华，增加雪原城关系200点
          needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
          addExp = 200;
          relationType = RelationTypeEnum.XueYuanCheng_Relation;
        }
        else if (idx == 3)
        {
          // 死灵魂魄精华，增加冰封城关系180点
          needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
          addExp = 180;
          relationType = RelationTypeEnum.BingFengCheng_Relation;
        }
        else if (idx == 4)
        {
          // 死灵魂魄精华，增加白马港关系150点
          needHunPoName = ThingNameEnum.SiLing_HunPo_JingHua;
          addExp = 150;
          relationType = RelationTypeEnum.BaiMaGang_Relation;
        }
        else if (idx == 5)
        {
          // 魁鬼魂魄精华，增加西赛平原关系200点
          needHunPoName = ThingNameEnum.KuiGui_HunPo_JingHua;
          addExp = 200;
          relationType = RelationTypeEnum.XiSaiPingYuan_Relation;
        }
        else if (idx == 6)
        {
          // 魁鬼魂魄精华，增加精灵世界关系150点
          needHunPoName = ThingNameEnum.KuiGui_HunPo_JingHua;
          addExp = 150;
          relationType = RelationTypeEnum.JingLingShiJie_Relation;
        }
        else if (idx == 7)
        {
          // 魔王魂魄精华，增加盖亚之城关系200点
          needHunPoName = ThingNameEnum.MoWang_HunPo_JingHua;
          addExp = 200;
          relationType = RelationTypeEnum.GaiYaZhiCheng_Relation;
        }
        if (needHunPoName == null)
        {
          return new ShowToastOut("服务器错误，请反馈给管理员！");
        }
        Thing HunPo_JingHua = bagSystem.GetThingInBag(needHunPoName, null, null);
        if (HunPo_JingHua == null || HunPo_JingHua.num < 1)
        {
          return new ShowToastOut("背包中没有足够的" + needHunPoName.getName() + "！");
        }
        bagSystem.addThingNumWithSend(HunPo_JingHua, -1);
        relationProc.addRelationExp(userCacheInfo.userSystem.userCountInfo, relationType, addExp);
        return new ShowToastOut("您的" + relationType.getName() + "增加" + addExp + "点");
      }
    }
  }