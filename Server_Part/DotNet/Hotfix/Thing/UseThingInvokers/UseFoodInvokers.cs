namespace MaoYouJi
{
  [Invoke((long)ThingNameEnum.ChaoJue_HunPoZhiJing)]
  public class UseChaoJueHunPoZhiJingInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      MapNode mapNode = args.map;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState == LiveStateEnum.DEAD)
      {
        attackComponent.RemoveComponent<KilledComponent>();
        user.ReviveUser(attackComponent.maxBlood, attackComponent.maxBlue);
        mapNode.SendMessagesToMapUser(new ServerSendChatMsg()
        {
          chatType = ChatType.Local_Chat,
          content = "玩家<u>" + user.nickname + "</u>使用超绝魂魄之精复活并回满了状态！",
        });
        bagComponent.AddThingNumWithSend(args.thing, -1);
      }
      else if (attackComponent.LiveState == LiveStateEnum.FIGHTING)
      {
        InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
        if (inFightComponent == null)
        {
          return LogicRet.Failed("您当前状态无法使用超绝魂魄之精！");
        }
        LogicRet rlt = await inFightComponent.OutCallUseThing(args.thing);
        if (!rlt.IsSuccess)
        {
          return rlt;
        }
        user.SendToast("使用超绝魂魄之精成功！");
      }
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }
}