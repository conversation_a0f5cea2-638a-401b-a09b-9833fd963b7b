using System;
using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [Invoke((long)ThingNameEnum.DunDi_Fu)]
  public class UseDunDiFuInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
      if (inFightComponent != null)
      {
        LogicRet rlt = await inFightComponent.OutCallQuitAttack();
        if (!rlt.IsSuccess)
        {
          return rlt;
        }
      }
      List<MapNode> mapNodes = MapProcSystem.GetMapNodesFromCache(MapNameConstant.DaTaoShaDao);
      MapNode oldNode = user.GetParent<MapNode>();
      MapNode mapNode = RandomGenerator.RandomArray(mapNodes);
      LogicRet logicRet = user.GetComponent<MoveComponent>().MoveTo(mapNode);
      if (!logicRet.IsSuccess)
      {
        return logicRet;
      }
      string content = user.nickname + "掏出" + args.thing.name + "来大呼一声：土地公祝我！然后消失的无影无踪了。";
      string content2 = user.nickname + "从土里钻了出来！";
      oldNode.SendMessageToMapUser(new ServerSendChatMsg(content, ChatType.Local_Chat));
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(content2, ChatType.Local_Chat));
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用遁地符成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.GuaiShuNi_YueHui)]
  public class UseGuaiShuNiYueHuiInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      DaTaoShaActComp daTaoShaAct = GlobalInfoCache.Instance.daTaoShaActComp;
      MapNode mapNode = args.map;
      HashSet<long> nowUsers = new(daTaoShaAct.DaTaoShaUserList);
      nowUsers.Remove(user.Id);
      if (nowUsers.Count == 0)
      {
        return LogicRet.Failed("当前没有其他玩家！");
      }
      long catchUserId = RandomGenerator.RandomArray(nowUsers.ToList());
      User catchUser = GlobalInfoCache.Instance.GetOnlineUser(catchUserId);
      if (catchUser.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("使用失败！");
      }
      if (catchUser.GetComponent<AttackComponent>().LiveState == LiveStateEnum.FIGHTING)
      {
        InFightComponent inFightComponent = catchUser.GetComponent<AttackComponent>().GetComponent<InFightComponent>();
        if (inFightComponent != null)
        {
          LogicRet rlt = await inFightComponent.OutCallQuitAttack();
          if (!rlt.IsSuccess)
          {
            return rlt;
          }
        }
      }
      MapNode oldNode = catchUser.GetParent<MapNode>();
      string content = "空中伸出了一只巨手，将" + catchUser.nickname + "抓走了！",
          content2 = catchUser.nickname + "被怪蜀黍抓了过来！";
      oldNode.SendMessageToMapUser(new ServerSendChatMsg(content, ChatType.Local_Chat));
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(content2, ChatType.Local_Chat));
      catchUser.SendToast("您被怪蜀黍抓到了" + mapNode.pointName);
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用怪蜀黍的约会成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.SiSi_Zhou)]
  public class UseSiSiZhouInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
      FightInfo attackTarget = inFightComponent?.attackTarget;
      if (inFightComponent == null || attackTarget == null)
      {
        return LogicRet.Failed("没有正在攻击中的玩家！");
      }
      User attackUser = GlobalInfoCache.Instance.GetOnlineUser(attackTarget.fightId);
      if (attackUser == null)
      {
        return LogicRet.Failed("攻击对象不存在！");
      }
      InFightComponent targetInFightComponent = attackUser.GetComponent<AttackComponent>().GetComponent<InFightComponent>();
      if (targetInFightComponent != null)
      {
        LogicRet rlt = await targetInFightComponent.OutCallQuitAttack();
        if (!rlt.IsSuccess)
        {
          return rlt;
        }
      }
      List<MapNode> mapNodes = MapProcSystem.GetMapNodesFromCache(MapNameConstant.DaTaoShaDao);
      MapNode oldNode = user.GetParent<MapNode>();
      MapNode mapNode = RandomGenerator.RandomArray(mapNodes);
      LogicRet logicRet = attackUser.GetComponent<MoveComponent>().MoveTo(mapNode);
      if (!logicRet.IsSuccess)
      {
        return logicRet;
      }
      string content = user.nickname + "使用" + args.thing.name + "将" + attackUser.nickname + "打入了未知之地！",
          content2 = attackUser.nickname + "从未知空间冒了出来！";
      oldNode.SendMessageToMapUser(new ServerSendChatMsg(content, ChatType.Local_Chat));
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(content2, ChatType.Local_Chat));
      attackUser.SendToast("您被" + user.nickname + "打入了未知之地！");
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用死死咒成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.DaTaoSha_FightTreasure)]
  public class UseDaTaoShaFightTreasureInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      InFightComponent inFightComponent = user.GetComponent<AttackComponent>().GetComponent<InFightComponent>();
      if (inFightComponent == null)
      {
        return LogicRet.Failed("战斗中才可使用该物品！");
      }
      LogicRet rlt = await inFightComponent.OutCallUseThing(args.thing);
      if (!rlt.IsSuccess)
      {
        return rlt;
      }
      user.SendToast("使用" + args.thing.name + "成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.OuBaSang_QingSuan)]
  public class UseOuBaSangQingSuanInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      if (user.GetComponent<AttackComponent>().LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("当前状态不可使用该物品！");
      }
      DaTaoShaActComp daTaoShaAct = GlobalInfoCache.Instance.daTaoShaActComp;
      if (daTaoShaAct.State != 2)
      {
        return LogicRet.Failed("当前阶段暂不可使用该道具！");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      if (mapNode.mapName != MapNameConstant.DaTaoShaDao)
      {
        return LogicRet.Failed("当前地图不是大逃杀岛！");
      }
      mapNode.nodeStates.Add(MapNodeState.ForBidden);
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(user.nickname + "使用了" + args.thing.name + "，该地区已变成禁区，请尽快远离！", ChatType.Local_Chat));
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用道具成功，该地区已变成禁区，请尽快远离！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.Sisi_ZhaiNan)]
  public class UseDaTaoShaZhiJingInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      if (user.GetComponent<AttackComponent>().LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("当前状态不可使用该物品！");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      int subNum = 0;
      HashSet<long> userIds = new(mapNode.userInPoint.Keys);
      userIds.Remove(user.Id);
      foreach (long userId in userIds)
      {
        User otherUser = GlobalInfoCache.Instance.GetOnlineUser(userId);
        BagComponent otherBagComponent = otherUser.GetComponent<BagComponent>();
        Thing thing = otherBagComponent.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
        if (thing != null && thing.num > 0)
        {
          otherUser.GetComponent<BagComponent>().AddThingNumWithSend(thing, -1);
          subNum++;
        }
      }
      if (subNum == 0)
      {
        return LogicRet.Failed("当前没有其他玩家拥有矿石！");
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      bagComponent.GiveThing(new ThingGiveInfo(ThingNameEnum.DaTaoSha_KuangShi, OwnType.PRIVATE, subNum));
      string content = user.nickname + "使用了" + args.thing.name + "从在场所有玩家的背包中各偷取了一个矿石！";
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(content, ChatType.Local_Chat));
      user.SendToast("使用道具成功，获得" + subNum + "个矿石！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.LuoLi_YuanNian)]
  public class UseLuoLiYuanNianInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      if (attackComponent.LiveState != LiveStateEnum.ALIVE)
      {
        return LogicRet.Failed("当前状态不可使用该物品！");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      List<long> monIds = new();
      foreach (var entry in mapNode.monInPoint)
      {
        if (entry.Value.liveState == LiveStateEnum.ALIVE)
        {
          monIds.Add(entry.Value.id);
        }
      }
      if (monIds.Count == 0)
      {
        return LogicRet.Failed("当前没有非战斗中的怪物！");
      }
      long monId = RandomGenerator.RandomArray(monIds);
      MonsterInfo monster = GlobalInfoCache.Instance.GetMonsterInfo(monId);
      if (monster == null)
      {
        return LogicRet.Failed("怪物信息获取失败！");
      }
      Treasure treasure = args.thing as Treasure;
      AttackComponent monAttack = monster.GetComponent<AttackComponent>();
      // 待补充，附加状态
      // AttachStatus attachStatus = treasure.targetStatus[0];
      // attachStatus.addInfo = user.GetComponent<InFightComponent>().GetFightInfo();
      // monster.nowAttack.attackState.put(AttackState.LuoLi_YuanNian, attachStatus);
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("成功给怪物" + monster.monName + " Lv." + monAttack.level + "施加萝莉控的怨念！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingNameEnum.MiHuan_Xiang)]
  public class UseMiHuanXiangInvoker : AInvokeHandler<UseThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseThingStruct args)
    {
      User user = args.user;
      if (user.activityName != ActNameEnum.Da_TaoSha)
      {
        return LogicRet.Failed("当前活动不是大逃杀！");
      }
      MapNode mapNode = user.GetParent<MapNode>();
      List<FightInfo> monFightInfos = new(), userFightInfos = new();
      foreach (var entry in mapNode.monInPoint)
      {
        if (entry.Value.liveState == LiveStateEnum.ALIVE)
        {
          monFightInfos.Add(new FightInfo()
          {
            fightId = entry.Value.id,
            liveType = LiveType.MONSTER,
            monBaseType = entry.Value.monBaseType,
            fightName = entry.Value.monName,
          });
        }
      }

      foreach (var entry in mapNode.userInPoint)
      {
        if (entry.Value.liveState == LiveStateEnum.ALIVE)
        {
          userFightInfos.Add(new FightInfo()
          {
            fightId = entry.Value.id,
            liveType = LiveType.ROLE,
            fightName = entry.Value.name,
          });
        }
      }
      if (monFightInfos.Count == 0 || userFightInfos.Count == 0)
      {
        return LogicRet.Failed("当前没有非战斗中的怪物和玩家！");
      }
      MapAttackManage mapAttackManage = mapNode.GetComponent<MapAttackManage>();
      foreach (FightInfo fightInfo in userFightInfos)
      {
        if (monFightInfos.Count == 0)
        {
          break;
        }
        FightInfo randMon = RandomGenerator.RandomArray(monFightInfos);
        monFightInfos.Remove(randMon);
        try
        {
          MonsterInfo monster = GlobalInfoCache.Instance.GetMonsterInfo(randMon.fightId);
          AttackComponent monsterAttack = monster.GetComponent<AttackComponent>();
          if (monsterAttack.LiveState != LiveStateEnum.ALIVE)
          {
            continue;
          }
          User targetUser = GlobalInfoCache.Instance.GetOnlineUser(fightInfo.fightId);
          if (targetUser == null || targetUser.GetComponent<AttackComponent>().LiveState != LiveStateEnum.ALIVE)
          {
            continue;
          }
          mapAttackManage.StartAttackBase(monsterAttack, targetUser.GetComponent<AttackComponent>()).Coroutine();
        }
        catch (Exception e)
        {
          ETLog.Error(e);
          continue;
        }
      }
      string content = user.nickname + "使用了" + args.thing.name + "，地图中的怪物都陷入了疯狂状态！";
      mapNode.SendMessageToMapUser(new ServerSendChatMsg(content, ChatType.Local_Chat));
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bagComponent.AddThingNumWithSend(args.thing, -1);
      user.SendToast("使用迷幻香成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }
}