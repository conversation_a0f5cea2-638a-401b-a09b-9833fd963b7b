using System;

namespace MaoYouJi
{
  [Invoke((long)ThingType.FOOD)]
  public class UseFoodInvoker : AInvokeHandler<UseMainTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseMainTypeThingStruct args)
    {
      Food food = (Food)args.thing;
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      InFightComponent inFightComponent = attackComponent.GetComponent<InFightComponent>();
      if (inFightComponent != null)
      {
        try
        {
          LogicRet rlt = await inFightComponent.OutCallUseThing(food);
          if (!rlt.IsSuccess)
          {
            return rlt;
          }
          // 使用成功，冷却所有同类型食物
          // if (ctx.outParams != null && ctx.outParams.isSuccess())
          // {
          //   bagSystem.coolAllFood(food.addType);
          // }
        }
        catch (Exception e)
        {
          ETLog.Error(e);
          return LogicRet.Failed("使用物品失败！");
        }
      }
      else if (attackComponent.LiveState == LiveStateEnum.ALIVE)
      {
        if (food.time == 0)
        {
          if (food.addType == FoodAddType.ADD_HP)
          {
            attackComponent.blood = Math.Min(attackComponent.blood + food.val, attackComponent.maxBlood);
          }
          else if (food.addType == FoodAddType.ADD_SP)
          {
            attackComponent.blue = Math.Min(attackComponent.blue + food.val, attackComponent.maxBlue);
          }
          user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
        }
        else
        {
          // userBaseProc.addCureTimer(userCacheInfo, food.addType, food.val, food.time);
        }
        bagComponent.AddThingNumWithSend(food, -1);
      }
      user.SendToast("使用" + food.name + "成功！");
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingType.TASK)]
  public class UseTaskInvoker : AInvokeHandler<UseMainTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseMainTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      MapNode mapNode = args.map;
      TaskThing taskThing = args.thing as TaskThing;
      // 检查是否在指定地点使用
      if (taskThing.usePoint != null && taskThing.useMap != null && (mapNode.pointName != taskThing.usePoint || mapNode.mapName != taskThing.useMap))
      {
        return LogicRet.Failed("请在" + taskThing.useMap + "/" + taskThing.usePoint + "使用此道具！");
      }
      // user.GetComponent<TaskComponent>().FillTaskRequire(TaskRequireType.Use_Thing_Cond, taskThing.thingName.ToString(), 1);
      bagComponent.AddThingNumWithSend(taskThing, -1);
      // 给予目标物品
      if (taskThing.targetThing != ThingNameEnum.None)
      {
        bagComponent.GiveThing(new ThingGiveInfo()
        {
          thingName = taskThing.targetThing,
          ownType = OwnType.PRIVATE,
          num = 1,
        });
      }
      // 召唤怪物
      if (taskThing.callMon != MonBaseType.None)
      {
        GenMonComponent genMonComponent = mapNode.GetComponent<GenMonComponent>();
        genMonComponent.GenOneNormalMon(taskThing.callMon, user);
      }
      user.SendToast("使用" + taskThing.name + "成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingType.TREASURE)]
  public class UseTreasureInvoker : AInvokeHandler<UseMainTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseMainTypeThingStruct args)
    {
      User user = args.user;
      Treasure treasure = args.thing as Treasure;
      if (treasure.canEnable())
      {
        if (!treasure.enabled)
        {
          treasure.enabled = true;
        }
        else
        {
          treasure.enabled = false;
        }
        ServerUpdateBagMsg serverUpdateBagMsg = new ServerUpdateBagMsg();
        serverUpdateBagMsg.updateList.Add(treasure);
        user.SendMessage(serverUpdateBagMsg);
        user.SendToast(treasure.enabled ? "激活" : "关闭" + treasure.name + "成功！");
        return LogicRet.Success;
      }
      await ETTask.CompletedTask;
      return LogicRet.Failed("该物品无法使用");
    }
  }
}