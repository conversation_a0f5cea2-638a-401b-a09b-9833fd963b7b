using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [Invoke((long)ThingSubType.JiNeng_JingYanShu)]
  public class UseSkillBookInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      ClientUseThingMsg msg = args.msg;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      if (msg.skillId == SkillIdEnum.None)
      {
        return LogicRet.Failed("请选择要增加经验的技能！");
      }
      SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
      Skill skill = skillComponent.GetSkill(msg.skillId);
      if (skill == null)
      {
        return LogicRet.Failed("您还没有学习该技能！");
      }
      if (skill.skillType != SkillTypeEnum.JOB_Base_SKILL)
      {
        return LogicRet.Failed("该技能无法增加经验！");
      }
      if (thing.vals != null && thing.vals.Length >= 2)
      {
        skillComponent.AddBaseSkillExp(skill, RandomGenerator.RandomNumber((int)thing.vals[0], (int)thing.vals[1]), false);
      }
      else
      {
        skillComponent.AddBaseSkillExp(skill, thing.val, false);
      }
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast("使用" + thing.name + "成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.JingYanShu)]
  public class UseExpBookInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      if (thing.vals != null && thing.vals.Length >= 2)
      {
        user.AddUserExp(RandomGenerator.RandomNumber((int)thing.vals[0], (int)thing.vals[1]), false);
      }
      else
      {
        user.AddUserExp(thing.val, false);
      }
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast("使用" + thing.name + "成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.CaiKuang_JiNeng_JingYanShu)]
  public class UseCaiKuangSkillBookInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
      Skill skill = skillComponent.GetSkill(SkillIdEnum.Cai_Kuang);
      if (skill == null)
      {
        return LogicRet.Failed("您还没有学习采矿技能！");
      }
      if (thing.vals != null && thing.vals.Length >= 2)
      {
        skillComponent.AddBaseSkillExp(skill, RandomGenerator.RandomNumber((int)thing.vals[0], (int)thing.vals[1]), false);
      }
      else
      {
        skillComponent.AddBaseSkillExp(skill, thing.val, false);
      }
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast("使用" + thing.name + "成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.ChongWu_ZhuanZhuan)]
  public class UseChongWuZhuanZhuanInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      RealRode realRode = (RealRode)args.thing;
      if (realRode.randEggs.Count == 0)
      {
        return LogicRet.Failed("转转蛋中的宠物蛋为空！");
      }
      if (!bagComponent.HasEnoughCapacity(1))
      {
        return LogicRet.Failed("背包空间不足！");
      }
      List<KeyValuePair<ThingNameEnum, int>> randEggs = new(realRode.randEggs.ToList());
      int total = 0;
      foreach (KeyValuePair<ThingNameEnum, int> entry in randEggs)
      {
        total += entry.Value;
      }
      int random = RandomGenerator.RandomNumber(0, total);
      int now = 0;
      ThingNameEnum randEgg = ThingNameEnum.None;
      foreach (KeyValuePair<ThingNameEnum, int> entry in randEggs)
      {
        now += entry.Value;
        if (random <= now)
        {
          randEgg = entry.Key;
          break;
        }
      }
      if (randEgg == ThingNameEnum.None)
      {
        ETLog.Error($"转转蛋中的宠物蛋为空！{realRode.randEggs.Count}, {total}, {random}, {now}");
        return LogicRet.Failed("转转蛋中的宠物蛋为空！");
      }
      ETLog.Info($"randEgg: {randEgg}, {total}, {random}, {now}");
      ThingGiveInfo thingGiveInfo = new()
      {
        thingName = randEgg,
        ownType = realRode.ownType,
        num = 1
      };
      Thing giveThing = bagComponent.GiveThing(thingGiveInfo);
      if (giveThing == null)
      {
        return LogicRet.Failed("获取宠物蛋失败！");
      }
      bagComponent.AddThingNumWithSend(realRode, -1);
      user.SendToast($"您获得了{giveThing.name}！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.ChongWu_Dan)]
  public class UseChongWuDanInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      RealRode realRode = (RealRode)args.thing;
      MapNode mapNode = args.map;
      if (mapNode.pointName != "宠物研究所")
      {
        return LogicRet.Failed("请在宠物研究所孵化宠物！");
      }
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      bool hasSkin = false;
      bool hasAllSkill = true;
      if (user.skinList.Contains(realRode.skinId))
      {
        hasSkin = true;
      }
      SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
      List<SkillIdEnum> skillIds = skillComponent.skillIds;
      foreach (SkillIdEnum skillIdEnum in realRode.skillIds)
      {
        if (!skillIds.Contains(skillIdEnum))
        {
          hasAllSkill = false;
        }
      }
      if (hasSkin && hasAllSkill)
      {
        return LogicRet.Failed("您已经拥有该宠物的皮肤和技能啦！");
      }
      if (!hasSkin)
      {
        user.skinList.Add(realRode.skinId);
      }
      if (!hasAllSkill)
      {
        foreach (SkillIdEnum skillIdEnum in realRode.skillIds)
        {
          if (skillIds.Contains(skillIdEnum))
          {
            continue;
          }
          Skill originSkill = GlobalInfoCache.Instance.GetSkill(skillIdEnum, 1);
          if (originSkill == null)
          {
            ETLog.Error($"宠物蛋中的技能{skillIdEnum}不存在！");
            continue;
          }
          skillComponent.AddLearnSkill(originSkill);
        }
      }
      bagComponent.AddThingNumWithSend(realRode, -1);
      user.SendToast("使用宠物蛋成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.Li_He)]
  public class UseLiHeInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Treasure treasure = (Treasure)args.thing;
      if (treasure.giveInfos == null || treasure.giveInfos.Count == 0)
      {
        ETLog.Error($"礼盒中没有物品！{treasure.thingId}");
        return LogicRet.Failed("礼盒中没有物品！");
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      List<ThingGiveInfo> giveInfos = new();
      foreach (ThingGiveInfo giveInfo in treasure.giveInfos)
      {
        if (giveInfo.job != BaseJob.None && !giveInfo.job.Equals(attackComponent.job))
        {
          continue;
        }
        giveInfos.Add(giveInfo);
      }
      if (giveInfos.Count == 0)
      {
        ETLog.Error($"礼盒为空！{treasure.thingId}, {user.Id}, {giveInfos.Count}, {treasure.giveInfos.Count}");
        return LogicRet.Failed("礼盒为空！");
      }
      bagComponent.AddThingNumWithSend(treasure, -1);
      bagComponent.GiveThing(giveInfos, ThingFromType.LiHe);
      user.SendToast("使用礼盒成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.YueGuangDe_ZhuFu)]
  public class UseYueGuangDeZhuFuInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      long existTime = 60 * 1000 * thing.val; // val为分钟
      UserStateDetail userStateDetail = new()
      {
        userState = UserStateEnum.ADD_EXP,
        existTime = existTime,
        endTime = TimeInfo.Instance.ServerNow() + existTime
      };
      int idx = 1;
      if (thing.thingName == ThingNameEnum.YueGuangDe_ZhuFu_Lv2)
      {
        idx = 2;
      }
      else if (thing.thingName == ThingNameEnum.YueGuangDe_ZhuFu_Lv3)
      {
        idx = 3;
      }
      userStateDetail.val = [2, idx];
      user.AddUserStatus(userStateDetail);
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast($"使用{thing.name}成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.QiangHua_ZhuFu)]
  public class UseQiangHuaZhuFuInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      int idx = 1;
      if (thing.thingName == ThingNameEnum.QiangHua_ZhuFu_Lv2)
      {
        idx = 2;
      }
      else if (thing.thingName == ThingNameEnum.QiangHua_ZhuFu_Lv3)
      {
        idx = 3;
      }
      if (!user.userStates.ContainsKey(UserStateEnum.ADD_EXP))
      {
        return LogicRet.Failed("您没有月光祝福状态");
      }
      UserStateDetail userStateDetail = user.userStates[UserStateEnum.ADD_EXP];
      if (userStateDetail.val[1] != idx)
      {
        return LogicRet.Failed("无法强化此月光祝福状态");
      }
      userStateDetail.val[0] = 3;
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.User_State));
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast($"使用{thing.name}成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.CiFu_GuangHui)]
  public class UseCiFuGuangHuiInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = args.thing;
      long existTime = 60 * 1000 * thing.val; // val为分钟
      UserStateDetail userStateDetail = new UserStateDetail();
      userStateDetail.userState = UserStateEnum.Add_All_Attr;
      userStateDetail.existTime = existTime;
      userStateDetail.endTime = TimeInfo.Instance.ServerNow() + existTime;
      userStateDetail.val = [50];
      user.AddUserStatus(userStateDetail);
      user.RecalcUserAttrs();
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      bagComponent.AddThingNumWithSend(thing, -1);
      user.SendToast($"使用{thing.name}成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }

  [Invoke((long)ThingSubType.ZhaoHuanFu)]
  public class UseZhaoHuanFuInvoker : AInvokeHandler<UseSubTypeThingStruct, ETTask<LogicRet>>
  {
    public override async ETTask<LogicRet> Handle(UseSubTypeThingStruct args)
    {
      User user = args.user;
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Treasure treasure = (Treasure)args.thing;
      if (treasure.callMon == MonBaseType.None)
      {
        return LogicRet.Failed("召唤符中没有召唤的怪物！");
      }
      MapNode mapNode = args.map;
      if (mapNode.nodeType == MapNodeType.CITY)
      {
        return LogicRet.Failed("请在野外使用召唤符！");
      }
      GenMonComponent genMonComponent = mapNode.GetComponent<GenMonComponent>();
      MonsterInfo monsterInfo = genMonComponent.GenOneNormalMon(treasure.callMon, user);
      if (monsterInfo == null)
      {
        return LogicRet.Failed("召唤失败！");
      }
      bagComponent.AddThingNumWithSend(treasure, -1);
      mapNode.SendMessageToMapUser(new ServerSendChatMsg
      {
        content = $"{user.nickname}掏出一张皱巴巴的符纸，口中念念有词，被封印的{monsterInfo.monName}感受到了遥远的召唤。",
        chatType = ChatType.Local_Chat
      });
      user.SendToast($"使用{treasure.name}成功！");
      await ETTask.CompletedTask;
      return LogicRet.Success;
    }
  }
}