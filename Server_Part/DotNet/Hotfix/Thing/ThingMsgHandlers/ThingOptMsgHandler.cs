using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class ClientUseThingMsgHandler : MessageLocationHandler<MapNode, ClientUseThingMsg>
  {
    protected override async ETTask Run(MapNode map, ClientUseThingMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = bagComponent.GetThingInBag<Thing>(msg.thingId);
      if (thing == null)
      {
        user.SendToast("物品不存在！");
        return;
      }
      if (thing.num <= 0)
      {
        user.SendToast("该物品数量不足！");
        return;
      }
      if (thing.coolTime != 0 && thing.coolTime > TimeInfo.Instance.ServerNow())
      {
        user.SendToast("该物品冷却中！");
        return;
      }
      if (attackComponent.LiveState != LiveStateEnum.ALIVE && !thing.canUse())
      {
        user.SendToast("当前状态无法使用该物品！");
        return;
      }
      if (attackComponent.LiveState == LiveStateEnum.FIGHTING && !thing.canUseInFight())
      {
        user.SendToast("该物品无法在战斗中使用！");
        return;
      }
      if (attackComponent.LiveState == LiveStateEnum.DEAD && !thing.canUseWhileDead())
      {
        user.SendToast("当前状态无法使用该物品！");
        return;
      }
      if (thing.minLevel != 0 && attackComponent.level < thing.minLevel)
      {
        user.SendToast("低于等级限制！");
        return;
      }
      if (thing.maxLevel != 0 && attackComponent.level > thing.maxLevel)
      {
        user.SendToast("超过等级限制！");
        return;
      }
      try
      {
        using (await user.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.UseThing, user.Id))
        {
          if (EventSystem.Instance.TryGetInvoke<UseThingStruct, ETTask<LogicRet>>((long)thing.thingName, out var userThingStructInvokeHandler))
          {
            logicRet = await userThingStructInvokeHandler.Handle(new UseThingStruct() { user = user, thing = thing, msg = msg, map = map });
          }
          else if (EventSystem.Instance.TryGetInvoke<UseSubTypeThingStruct, ETTask<LogicRet>>((long)thing.thingSubType, out var subInvokeHandler))
          {
            logicRet = await subInvokeHandler.Handle(new UseSubTypeThingStruct() { user = user, thing = thing, msg = msg, map = map });
          }
          else if (EventSystem.Instance.TryGetInvoke<UseMainTypeThingStruct, ETTask<LogicRet>>((long)thing.thingType, out var invokeHandler))
          {
            logicRet = await invokeHandler.Handle(new UseMainTypeThingStruct() { user = user, thing = thing, msg = msg, map = map });
          }
          else
          {
            user.SendToast("该物品无法使用！");
            return;
          }
        }
        if (!logicRet.IsSuccess)
        {
          user.SendToast(logicRet.Message);
          return;
        }
      }
      catch (Exception e)
      {
        ETLog.Error(e);
        user.SendToast("服务器内部错误");
      }
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientDropThingMsgHandler : MessageLocationHandler<MapNode, ClientDropThingMsg>
  {
    protected override async ETTask Run(MapNode map, ClientDropThingMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      if (msg.num <= 0)
      {
        user.SendToast("非法的丢弃数量！");
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing oneThing = bagComponent.GetThingInBag<Thing>(msg.thingId);
      if (oneThing == null)
      {
        user.SendToast("物品不存在！");
        return;
      }
      if (!oneThing.canDrop())
      {
        user.SendToast("该物品无法丢弃！");
        return;
      }
      if (msg.num > oneThing.num)
      {
        user.SendToast("丢弃数量多于拥有数量！");
        return;
      }
      if (oneThing.thingType == ThingType.EQUIP)
      {
        Equipment equipment = (Equipment)oneThing;
        foreach (Thing gem in equipment.gemList)
        {
          if (gem != null)
          {
            user.SendToast("装备有宝石，无法丢弃！");
            return;
          }
        }
      }

      ETLog.Info($"dropThing: {msg.thingId}, {msg.num}, {oneThing.thingName}");
      bagComponent.AddThingNumWithSend(oneThing, -msg.num);
      user.SendToast($"成功丢弃{msg.num}个{oneThing.name}");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientSellThingMsgHandler : MessageLocationHandler<MapNode, ClientSellThingMsg>
  {
    protected override async ETTask Run(MapNode map, ClientSellThingMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      if (msg.num <= 0)
      {
        user.SendToast("非法的出售数量！");
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing oneThing = bagComponent.GetThingInBag<Thing>(msg.thingId);
      if (oneThing == null)
      {
        user.SendToast("物品不存在！");
        return;
      }
      if (!oneThing.canSell())
      {
        user.SendToast("该物品无法出售！");
        return;
      }
      if (msg.num > oneThing.num)
      {
        user.SendToast("出售数量多于拥有数量！");
        return;
      }
      if (oneThing.thingType == ThingType.EQUIP)
      {
        Equipment equipment = (Equipment)oneThing;
        foreach (Thing gem in equipment.gemList)
        {
          if (gem != null)
          {
            user.SendToast("装备有宝石，无法出售！");
            return;
          }
        }
      }

      bagComponent.AddThingNumWithSend(oneThing, -msg.num);

      // 填充任务进度 - 出售物品条件
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      taskComponent.FillTaskRequire(TaskRequireType.Sell_Thing_Cond, oneThing.thingName.ToString(), msg.num);

      // 添加金币
      bagComponent.AddAllCoinWithSend(oneThing.salePrice * msg.num);

      user.SendToast($"成功出售{msg.num}个{oneThing.name}");
      await ETTask.CompletedTask;
    }
  }
}