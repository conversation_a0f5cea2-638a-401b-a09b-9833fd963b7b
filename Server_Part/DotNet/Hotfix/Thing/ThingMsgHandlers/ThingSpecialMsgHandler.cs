using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class ClientSavePointMsgHandler : MessageLocationHandler<MapNode, ClientSavePointMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientSavePointMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Treasure yuMao = bagComponent.GetThingInBag<Treasure>(ThingNameEnum.JiYi_YuMao);
      MapNode mapNode = user.GetParent<MapNode>();
      if (yuMao == null)
      {
        user.SendToast("记忆羽毛不存在！");
        return;
      }
      if (mapNode.mapName == "幼稚园")
      {
        user.SendToast("幼稚园不能保存保存点！");
        return;
      }
      if (MapConstant.canNotUseFastTransferMap.Contains(mapNode.mapName))
      {
        user.SendToast("无法保存此地点！");
        return;
      }
      if (mapNode.nodeType == MapNodeType.CITY)
      {
        foreach (PointInfo pointInfo in yuMao.cityPoints)
        {
          if (pointInfo.mapName == mapNode.mapName && pointInfo.pointName == mapNode.pointName)
          {
            user.SendToast("该地点已被保存在记忆羽毛中！");
            return;
          }
        }
        if (yuMao.cityPoints.Count >= yuMao.maxPointNum)
        {
          user.SendToast("城市保存点已达上限！");
          return;
        }
        yuMao.cityPoints.Add(new PointInfo { mapName = mapNode.mapName, pointName = mapNode.pointName });
      }
      else
      {
        foreach (PointInfo pointInfo in yuMao.outPoints)
        {
          if (pointInfo.mapName == mapNode.mapName && pointInfo.pointName == mapNode.pointName)
          {
            user.SendToast("该地点已被保存在记忆羽毛中！");
            return;
          }
        }
        if (yuMao.outPoints.Count >= yuMao.maxPointNum)
        {
          user.SendToast("野外保存点已达上限！");
          return;
        }
        yuMao.outPoints.Add(new PointInfo { mapName = mapNode.mapName, pointName = mapNode.pointName });
      }

      user.SendToast("添加保存点成功");
      ServerUpdateBagMsg updateBagThingsOut = new ServerUpdateBagMsg();
      updateBagThingsOut.updateList.Add(yuMao);
      user.SendMessage(updateBagThingsOut);

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientDelSavePointMsgHandler : MessageLocationHandler<MapNode, ClientDelSavePointMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDelSavePointMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Treasure yuMao = bagComponent.GetThingInBag<Treasure>(ThingNameEnum.JiYi_YuMao);
      if (yuMao == null)
      {
        user.SendToast("记忆羽毛不存在！");
        return;
      }
      List<PointInfo> srcInfos = yuMao.cityPoints;

      if (msg.isOut)
      {
        srcInfos = yuMao.outPoints;
      }

      bool find = false;
      foreach (PointInfo info in srcInfos)
      {
        if (info.mapName == msg.delMap && info.pointName == msg.delPoint)
        {
          find = true;
          srcInfos.Remove(info);
          break;
        }
      }

      if (!find)
      {
        user.SendToast("该地点不在保存点中！");
        return;
      }

      user.SendToast("删除保存点成功");
      ServerUpdateBagMsg updateBagThingsOut = new ServerUpdateBagMsg();
      updateBagThingsOut.updateList.Add(yuMao);
      user.SendMessage(updateBagThingsOut);

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientQuickFoodMsgHandler : MessageLocationHandler<MapNode, ClientQuickFoodMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientQuickFoodMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent.GetComponent<SkillComponent>();

      if (msg.quickFoods == null || msg.quickFoods.Count == 0)
      {
        user.SendToast("没有选择要快捷的物品！");
        return;
      }

      // 初始化快捷栏
      if (skillComponent.quickFoods == null)
      {
        skillComponent.quickFoods = new long[10];
      }

      foreach (var entry in msg.quickFoods)
      {
        long thingId = entry.Key;
        int quickIdx = entry.Value;

        if (quickIdx < 0 || quickIdx >= skillComponent.quickFoods.Length)
        {
          user.SendToast("快捷栏位置非法！");
          return;
        }

        Thing thing = bagComponent.GetThingInBag<Thing>(thingId);
        if (thing == null)
        {
          user.SendToast("快捷栏中的物品不存在！");
          return;
        }

        // 检查物品是否可以快捷使用
        bool canQuickUse = true;
        if (!thing.canUse() && !thing.canUseInFight() && !ThingConstant.IsSpecialQuickThing(thing.thingName))
        {
          canQuickUse = false;
        }

        if (!canQuickUse)
        {
          user.SendToast("该物品无法快捷使用！");
          return;
        }

        // 移除该物品在其他位置的快捷设置
        for (int i = 0; i < skillComponent.quickFoods.Length; i++)
        {
          if (skillComponent.quickFoods[i] == thingId)
          {
            skillComponent.quickFoods[i] = 0;
          }
        }

        // 设置新的快捷位置
        skillComponent.quickFoods[quickIdx] = thingId;
      }

      // 发送更新消息
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Quick_Bar));
      user.SendToast("快捷物品设置成功");

      await ETTask.CompletedTask;
    }
  }
}