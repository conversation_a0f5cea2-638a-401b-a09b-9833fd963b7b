using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class ShowAccountBagMsgHandler : MessageLocationHandler<MapNode, ShowAccountBagReq, ShowAccountBagResp>
  {
    protected override async ETTask Run(MapNode map, ShowAccountBagReq req, ShowAccountBagResp resp)
    {
      LogicRet logicRet = map.GetUserWithCheck(req.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        resp.SetError(logicRet.Message);
        return;
      }
      Account account = await user.GetAccount();
      if (account == null)
      {
        ETLog.Error($"ShowAccountBagReq: 账号不存在, userId: {req.UserId}");
        resp.SetError("账号不存在");
        return;
      }
      BagComponent bagComponent = account.GetComponent<BagComponent>();
      bagComponent ??= account.AddComponent<BagComponent, BagType, int>(BagType.PublicStore, 20);
      resp.bagDaoInfo = bagComponent.GetBagDaoInfo();
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientSaveToAccountBagMsgHandler : MessageLocationHandler<MapNode, ClientSaveToAccountBagMsg>
  {
    protected override async ETTask Run(MapNode map, ClientSaveToAccountBagMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      Account account = await user.GetAccount();
      if (account == null)
      {
        ETLog.Error($"ClientSaveToAccountBagMsg: 账号不存在, userId: {msg.UserId}");
        user?.SendToast("账号不存在");
        return;
      }
      BagComponent accountBag = account.GetComponent<BagComponent>();
      BagComponent userBag = user.GetComponent<BagComponent>();
      if (msg.thingIds == null || msg.thingIds.Count == 0)
      {
        user?.SendToast("没有选择要保存的物品！");
        return;
      }
      List<Thing> things = userBag.GetThings(msg.thingIds.ToArray());
      if (things.Count != msg.thingIds.Count)
      {
        user?.SendToast("物品不存在！");
        return;
      }
      foreach (Thing thing in things)
      {
        if (thing.ownType == OwnType.PRIVATE)
        {
          user?.SendToast("该物品无法保存到账户仓库！");
          return;
        }
      }
      bool hasEnoughCapacity = accountBag.HasEnoughCapacity(accountBag.NeedRemainCapacity(things));
      if (!hasEnoughCapacity)
      {
        user?.SendToast("账户仓库容量不足！");
        return;
      }
      accountBag.GiveThingList(things);
      userBag.RemoveThings(msg.thingIds);
      ServerUpdateBagMsg serverUpdateBagMsg = new()
      {
        removeThingIds = msg.thingIds
      };
      ServerUpdateAccountBagMsg serverUpdateAccountBagMsg = new()
      {
        updateList = things
      };
      user.SendMessage(serverUpdateBagMsg, serverUpdateAccountBagMsg);
      user.SendToast("保存成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientGetFromAccountBagMsgHandler : MessageLocationHandler<MapNode, ClientGetFromAccountBagMsg>
  {
    protected override async ETTask Run(MapNode map, ClientGetFromAccountBagMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      if (msg.num <= 0)
      {
        user?.SendToast("非法的数量");
        return;
      }
      Account account = await user.GetAccount();
      if (account == null)
      {
        ETLog.Error($"ClientGetFromAccountBagMsg: 账号不存在, userId: {msg.UserId}");
        user?.SendToast("账号不存在");
        return;
      }
      BagComponent accountBag = account.GetComponent<BagComponent>();
      BagComponent userBag = user.GetComponent<BagComponent>();

      Thing targetThing = accountBag.GetThingInBag<Thing>(msg.thingId);
      if (targetThing == null)
      {
        user?.SendToast("物品不在账户仓库中！");
        return;
      }
      if (targetThing.num < msg.num)
      {
        user?.SendToast("物品数量不足！");
        return;
      }
      if (userBag.thingMap.Count >= userBag.capacity)
      {
        user?.SendToast("背包容量不足！");
        return;
      }
      accountBag.AddThingNumWithSend(targetThing, -msg.num);
      ServerUpdateAccountBagMsg serverUpdateAccountBagMsg = new();
      userBag.GiveThingList(new List<Thing> { targetThing });
      if (targetThing.num <= 0)
      {
        serverUpdateAccountBagMsg.removeThingIds.Add(targetThing.thingId);
      }
      else
      {
        serverUpdateAccountBagMsg.updateList.Add(targetThing);
      }
      user.SendMessage(serverUpdateAccountBagMsg);
      user.SendToast("获取成功");
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientAddBagCapacityMsgHandler : MessageLocationHandler<MapNode, ClientAddBagCapacityMsg>
  {
    protected override async ETTask Run(MapNode map, ClientAddBagCapacityMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      BagComponent bag = user.GetComponent<BagComponent>();
      if (msg.addType == 0)
      {
        int addCapacity = msg.costCatBean * 10;
        if (bag.capacity + addCapacity > 500)
        {
          user?.SendToast("背包容量超过上限！");
          return;
        }
        if (bag.catBean < msg.costCatBean)
        {
          user?.SendToast("猫豆不足！");
          return;
        }
        bag.capacity += addCapacity;
        bag.catBean -= msg.costCatBean;
        bag.SendNewCoin();
        user.SendToast("您的背包容量增加了" + addCapacity + "！");
      }
      else
      {
        Account account = await user.GetAccount();
        if (account == null)
        {
          ETLog.Error($"ClientAddBagCapacityMsg: 账号不存在, userId: {msg.UserId}");
          user?.SendToast("账号不存在");
          return;
        }
        BagComponent accountBag = account.GetComponent<BagComponent>();
        int addCapacity = msg.costCatBean * 5;
        if (accountBag.capacity + addCapacity > 300)
        {
          user?.SendToast("账户仓库容量超过上限！");
          return;
        }
        if (bag.catBean < msg.costCatBean)
        {
          user?.SendToast("猫豆不足！");
          return;
        }
        accountBag.capacity += addCapacity;
        bag.catBean -= msg.costCatBean;
        accountBag.SendNewCoin();
        user.SendToast("您的账户仓库容量增加了" + addCapacity + "！");
      }
      user.SendToast("扩容成功");
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientPutInPackMsgHandler : MessageLocationHandler<MapNode, ClientPutInPackMsg>
  {
    protected override async ETTask Run(MapNode map, ClientPutInPackMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      BagComponent bag = user.GetComponent<BagComponent>();
      Treasure backPack = bag.GetThingInBag<Treasure>(msg.bagPackId);
      if (backPack == null)
      {
        user?.SendToast("背包不存在");
        return;
      }
      if (backPack.thingSubType != ThingSubType.Bag || backPack.capacity == 0 || backPack.thingsInBag == null)
      {
        user?.SendToast("该物品不是包裹");
        return;
      }
      if (backPack.thingsInBag.Count >= backPack.capacity)
      {
        user?.SendToast("便携背包已满");
        return;
      }
      Thing thing = bag.GetThingInBag<Thing>(msg.thingId);
      if (thing == null)
      {
        user?.SendToast("物品不存在");
        return;
      }
      if (thing.thingSubType == ThingSubType.Bag)
      {
        user?.SendToast("不能放入包裹");
        return;
      }
      Thing preCanStackThing = null;
      foreach (Thing t in backPack.thingsInBag)
      {
        if (t.thingName == thing.thingName && t.grade == thing.grade && t.ownType == thing.ownType
            && t.stackNum > t.num)
        {
          preCanStackThing = t;
          break;
        }
      }
      if (preCanStackThing == null && backPack.thingsInBag.Count >= backPack.capacity)
      {
        user?.SendToast("便携背包已满");
        return;
      }
      if (preCanStackThing != null)
      {
        preCanStackThing.num += thing.num;
      }
      else
      {
        backPack.thingsInBag.Add(thing);
      }
      bag.RemoveThing(thing);
      ServerUpdateBagMsg serverUpdateBagMsg = new()
      {
        updateList = new List<Thing> { backPack },
        removeThingIds = new List<long> { thing.thingId }
      };
      user.SendMessage(serverUpdateBagMsg);
      user.SendToast("将" + thing.thingName + "×" + thing.num + "放入便携背包");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientGetFromPackMsgHandler : MessageLocationHandler<MapNode, ClientGetFromPackMsg>
  {
    protected override async ETTask Run(MapNode map, ClientGetFromPackMsg msg)
    {
      LogicRet logicRet = map.GetUserWithCheck(msg.UserId, out User user, true, false);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }
      BagComponent bag = user.GetComponent<BagComponent>();
      Treasure backPack = bag.GetThingInBag<Treasure>(msg.bagPackId);
      if (backPack == null)
      {
        user?.SendToast("便携背包不存在");
        return;
      }
      if (backPack.thingSubType != ThingSubType.Bag || backPack.capacity == 0 || backPack.thingsInBag == null)
      {
        user?.SendToast("该物品不是包裹");
        return;
      }
      if (backPack.thingsInBag.Count == 0)
      {
        user?.SendToast("背包为空");
        return;
      }
      List<Thing> targetThings = new();
      if (msg.isAll)
      {
        targetThings.AddRange(backPack.thingsInBag);
      }
      else
      {
        foreach (Thing t in backPack.thingsInBag)
        {
          if (t.thingId == msg.thingId)
          {
            targetThings.Add(t);
            break;
          }
        }
      }
      if (targetThings.Count == 0)
      {
        user?.SendToast("物品不存在");
        return;
      }
      int needRemainCapacity = bag.NeedRemainCapacity(targetThings);
      if (!bag.HasEnoughCapacity(needRemainCapacity))
      {
        user?.SendToast("背包容量不足");
        return;
      }
      bag.GiveThingList(targetThings);
      backPack.thingsInBag.RemoveAll(targetThings.Contains);
      ServerUpdateBagMsg serverUpdateBagMsg = new()
      {
        updateList = new List<Thing> { backPack }
      };
      user.SendMessage(serverUpdateBagMsg);
      user.SendToast(msg.isAll ? "取出所有物品成功" : "取出" + targetThings[0].thingName + "×" + targetThings[0].num + "成功");
      await ETTask.CompletedTask;
    }
  }
}