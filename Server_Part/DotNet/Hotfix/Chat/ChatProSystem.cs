using System.Collections.Generic;
using System.Text;

namespace Mao<PERSON>ouJi
{
  public static class ChatProSystem
  {
    public static void SendMessageToUser(long userId, params MaoYouMessage[] messages)
    {
      User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      user?.SendMessage(messages);
    }

    public static void SendMessageToUsers(List<long> userIds, params MaoYouMessage[] messages)
    {
      foreach (var userId in userIds)
      {
        SendMessageToUser(userId, messages);
      }
    }

    public static void SendGlobalNotice(string content, ChatType chatType, string nickName = null)
    {
      ServerShowNoticeMsg msg = new()
      {
        content = content,
        chatType = chatType,
        nickName = nickName
      };
      SendMessageToAllUser(msg);
    }

    public static void SendMessageToAllUser(params MaoYouMessage[] messages)
    {
      foreach (var user in GlobalInfoCache.Instance.allOnlineUserCache)
      {
        user.Value?.SendMessage(messages);
      }
    }

    public static void SendChatToLocalUsers(string mapName, params MaoYouMessage[] messages)
    {
      List<MapNode> mapNodes = MapProcSystem.GetMapNodesFromCache(mapName, true);
      foreach (var mapNode in mapNodes)
      {
        mapNode.SendMessagesToMapUser(messages);
      }
    }

    public static void SendChatToUser(long userId, string content, ChatType chatType = ChatType.Sys_Chat)
    {
      User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
      user?.SendChat(content, chatType);
    }

    public static void SendChatToUsers(List<long> userIds, string content, ChatType chatType = ChatType.Sys_Chat)
    {
      foreach (var userId in userIds)
      {
        SendChatToUser(userId, content, chatType);
      }
    }

    public static string getCoinNumStr(long num)
    {
      StringBuilder sb = new StringBuilder();
      if (num >= 10000)
      {
        sb.Append(num / 10000 + "金");
        num %= 10000;
      }
      if (num >= 100)
      {
        sb.Append(num / 100 + "银");
        num %= 100;
      }
      if (num > 0)
      {
        sb.Append(num + "铜");
      }
      return sb.ToString();
    }
  }
}
