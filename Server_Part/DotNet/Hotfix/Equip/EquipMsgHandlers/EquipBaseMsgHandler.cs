using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class UserEquipThingMsgHandler : MessageLocationHandler<MapNode, EquipThingMsg>
  {
    protected override async ETTask Run(MapNode nowMap, EquipThingMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      // 获取装备
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = bagComponent.GetThingInBag<Thing>(req.thingId);
      if (thing == null)
      {
        user.SendToast("装备不存在背包中");
        return;
      }

      ServerUpdateBagMsg updateBagThingsOut = new()
      {
        updateList = new List<Thing>(),
        removeThingIds = new List<long>()
      };
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      if (thing.thingType == ThingType.EQUIP)
      {
        Equipment equipment = (Equipment)thing;
        Equipment preEquip = null;

        if (equipment.baseJob != BaseJob.None && equipment.baseJob != user.GetComponent<AttackComponent>().job)
        {
          user.SendToast("职业不匹配");
          return;
        }
        if (equipment.level > user.GetComponent<AttackComponent>().level)
        {
          user.SendToast("等级不足");
          return;
        }

        // 双手武器不能装备盾牌
        if (equipment.equipPart == EquipPart.Wu_Qi)
        {
          if (equipment.isTwoHand())
          {
            foreach (Equipment equip in equipComponent.equipList)
            {
              if (equip != null && equip.equipPart == EquipPart.Dun_Pai)
              {
                user.SendToast("双手武器不能装备盾牌");
                return;
              }
            }
          }
        }
        else if (equipment.equipPart == EquipPart.Dun_Pai)
        {
          foreach (Equipment equip in equipComponent.equipList)
          {
            if (equip != null && equip.equipPart == EquipPart.Wu_Qi && equip.isTwoHand())
            {
              user.SendToast("双手武器不能装备盾牌");
              return;
            }
          }
        }

        int preIdx = equipComponent.GetUserEquip(equipment.equipPart);
        if (equipment.ownType == OwnType.PUBLIC || equipment.ownType == OwnType.SHARE)
        {
          equipment.ownType = OwnType.PRIVATE;
        }

        if (preIdx != -1)
        {
          preEquip = equipComponent.equipList[preIdx];
          bagComponent.AddThing(preEquip);
          updateBagThingsOut.updateList.Add(preEquip);
          equipComponent.equipList[preIdx] = equipment;
        }
        else
        {
          equipComponent.equipList.Add(equipment);
        }

        // 注释掉未找到的功能
        // equipProc.getActiveEquipBonus(user.nowAttack);
        bagComponent.RemoveThing(thing);
        updateBagThingsOut.removeThingIds.Add(thing.thingId);
        user.RecalcUserAttrs();
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
            UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }
      else if (EquipConstant.IsSpecialEquip(thing.thingSubType))
      {
        // 处理特殊装备（如护符等）
        if (thing.ownType == OwnType.PUBLIC || thing.ownType == OwnType.SHARE)
        {
          thing.ownType = OwnType.PRIVATE;
        }
        int preIdx = equipComponent.GetUserSpecialEquipIdx(thing.thingSubType);
        if (preIdx != -1)
        {
          Thing preEquip = equipComponent.specialEquipList[preIdx];
          bagComponent.AddThing(preEquip);
          updateBagThingsOut.updateList.Add(preEquip);
          equipComponent.specialEquipList[preIdx] = thing;
        }
        else
        {
          equipComponent.specialEquipList.Add(thing);
        }
        bagComponent.RemoveThing(thing);
        updateBagThingsOut.removeThingIds.Add(thing.thingId);
        user.RecalcUserAttrs();
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
            UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }

      user.SendMessage(updateBagThingsOut);
      user.SendToast("装备" + thing.name + "成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserUnEquipThingMsgHandler : MessageLocationHandler<MapNode, UnEquipThingMsg>
  {
    protected override async ETTask Run(MapNode nowMap, UnEquipThingMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      Thing thing = null;

      foreach (Equipment equipEle in equipComponent.equipList)
      {
        if (equipEle.thingId == req.thingId)
        {
          thing = equipEle;
          equipComponent.equipList.Remove(equipEle);
          break;
        }
      }

      foreach (Thing specialEquip in equipComponent.specialEquipList)
      {
        if (specialEquip.thingId == req.thingId)
        {
          thing = specialEquip;
          equipComponent.specialEquipList.Remove(specialEquip);
          break;
        }
      }

      if (thing == null)
      {
        user.SendToast("未装备该物品");
        return;
      }
      else
      {
        bagComponent.AddThing(thing);
      }

      // 注释掉未找到的功能
      // equipProc.getActiveEquipBonus(user.nowAttack);
      user.RecalcUserAttrs();
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
          UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      user.SendToast("卸下装备" + thing.name + "成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserDrillEquipMsgHandler : MessageLocationHandler<MapNode, DrillEquipMsg>
  {
    protected override async ETTask Run(MapNode nowMap, DrillEquipMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing drillingItem = bagComponent.GetThingInBag<Thing>(req.drillingItemId);
      if (drillingItem == null || drillingItem.num <= 0)
      {
        user.SendToast("背包中的打孔器不足");
        return;
      }
      if (drillingItem.thingName != ThingNameEnum.Drilling_Item)
      {
        user.SendToast("该物品不是打孔器");
        return;
      }
      Thing equip = bagComponent.GetThingInBag<Thing>(req.equipId);
      if (equip == null)
      {
        user.SendToast("装备不存在");
        return;
      }
      if (equip.thingType != ThingType.EQUIP)
      {
        user.SendToast("该物品不是装备");
        return;
      }
      Equipment equipment = (Equipment)equip;
      if (equipment.level < 10)
      {
        user.SendToast("10级以上的装备才能打孔");
        return;
      }
      if (equipment.grade == ThingGrade.NORMAL && equipment.maxGemNum >= 1)
      {
        user.SendToast("普通装备最多打一个孔");
        return;
      }
      if (equipment.grade == ThingGrade.GOOD && equipment.maxGemNum >= 2)
      {
        user.SendToast("良好装备最多打两个孔");
        return;
      }
      if (equipment.maxGemNum >= 3)
      {
        user.SendToast("装备最多打三个孔");
        return;
      }

      bagComponent.AddThingNumWithSend(drillingItem, -1);
      equipment.maxGemNum++;

      ServerUpdateBagMsg updateBagThingsOut = new()
      {
        updateList = new List<Thing> { equipment },
        removeThingIds = new List<long>()
      };
      user.SendMessage(updateBagThingsOut);
      user.SendToast("打孔成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserAddGemMsgHandler : MessageLocationHandler<MapNode, AddGemMsg>
  {
    protected override async ETTask Run(MapNode nowMap, AddGemMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();

      Thing gemThing = bagComponent.GetThingInBag<Thing>(req.gemId);
      if (gemThing == null)
      {
        user.SendToast("宝石不存在");
        return;
      }
      if (gemThing.thingType != ThingType.MATERIAL)
      {
        user.SendToast("该物品不是宝石");
        return;
      }
      Material gem = (Material)gemThing;
      if (gem.gemType == GemType.None)
      {
        user.SendToast("该物品不是宝石");
        return;
      }

      Equipment equipment = null;
      if (!req.isEquiped)
      {
        equipment = bagComponent.GetThingInBag<Equipment>(req.equipId);
      }
      else
      {
        equipment = equipComponent.GetUserEquip(req.equipId);
      }

      if (equipment == null)
      {
        user.SendToast("装备不存在");
        return;
      }
      if (req.gemSlot >= equipment.maxGemNum)
      {
        user.SendToast("宝石槽位不足");
        return;
      }
      if (!EquipConstant.EquipCanGems.ContainsKey(equipment.equipPart))
      {
        user.SendToast("该装备不能镶嵌宝石");
        return;
      }
      if (!EquipConstant.EquipCanGems[equipment.equipPart].Contains(gem.gemType))
      {
        user.SendToast("该装备不能镶嵌该宝石");
        return;
      }
      if (gem.minLevel > 0 && gem.minLevel > equipment.level)
      {
        user.SendToast("必须" + gem.minLevel + "级以上的装备才能镶嵌该宝石");
        return;
      }

      if (equipment.gemList[req.gemSlot] != null)
      {
        if (!bagComponent.HasEnoughCapacity(1))
        {
          user.SendToast("背包空间不足");
          return;
        }
        // 如果宝石槽位有宝石，则将宝石添加到背包中
        bagComponent.GiveThingList([equipment.gemList[req.gemSlot]]);
      }
      bagComponent.AddThingNumWithSend(gem, -1);

      Material copyGem = gem.Clone() as Material;
      copyGem.num = 1;
      copyGem.thingId = IdGenerater.Instance.GenerateId();
      equipment.gemList[req.gemSlot] = copyGem;
      equipment.RecalEquipAttrs();

      if (!req.isEquiped)
      {
        ServerUpdateBagMsg updateBagThingsOut = new()
        {
          updateList = new List<Thing> { equipment },
          removeThingIds = new List<long>()
        };
        user.SendMessage(updateBagThingsOut);
      }
      else
      {
        user.RecalcUserAttrs();
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
            UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }

      user.SendToast("镶嵌宝石成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserRemoveGemMsgHandler : MessageLocationHandler<MapNode, RemoveGemMsg>
  {
    protected override async ETTask Run(MapNode nowMap, RemoveGemMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      Equipment equipment;

      if (!req.isEquiped)
      {
        equipment = bagComponent.GetThingInBag<Equipment>(req.equipId);
      }
      else
      {
        equipment = equipComponent.GetUserEquip(req.equipId);
      }

      if (equipment == null)
      {
        user.SendToast("装备不存在");
        return;
      }
      if (req.gemSlot >= equipment.maxGemNum || equipment.gemList.Length <= req.gemSlot)
      {
        user.SendToast("宝石槽位不足");
        return;
      }
      if (equipment.gemList[req.gemSlot] == null)
      {
        user.SendToast("该槽位没有宝石");
        return;
      }
      if (!bagComponent.HasEnoughCapacity(1))
      {
        user.SendToast("背包空间不足");
        return;
      }

      // 将卸下的宝石放到背包中
      bagComponent.GiveThingList([equipment.gemList[req.gemSlot]]);
      equipment.gemList[req.gemSlot] = null;
      equipment.RecalEquipAttrs();

      if (!req.isEquiped)
      {
        ServerUpdateBagMsg updateBagThingsOut = new()
        {
          updateList = new List<Thing> { equipment },
          removeThingIds = new List<long>()
        };
        user.SendMessage(updateBagThingsOut);
      }
      else
      {
        user.RecalcUserAttrs();
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
            UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }

      user.SendToast("卸下宝石成功");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserEnhanceEquipMsgHandler : MessageLocationHandler<MapNode, EnhanceEquipMsg>
  {
    protected override async ETTask Run(MapNode nowMap, EnhanceEquipMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 获取装备
      Equipment equipment = bagComponent.GetThingInBag<Equipment>(req.equipId);
      if (equipment == null)
      {
        user.SendToast("装备不存在");
        return;
      }

      // 获取升级石
      Treasure shengJiShi = bagComponent.GetThingInBag<Treasure>(req.shengJiShiId);
      if (shengJiShi == null || shengJiShi.num <= 0)
      {
        user.SendToast("升级石不在背包中");
        return;
      }
      if (shengJiShi.thingSubType != ThingSubType.ShengJiShi &&
          shengJiShi.thingSubType != ThingSubType.GaoJi_ShengJiShi &&
          shengJiShi.thingSubType != ThingSubType.ChuJi_ShengJiShi)
      {
        user.SendToast("该物品不是升级石");
        return;
      }

      // 获取完璧宝玉（可选）
      Thing wanBi = null;
      if (req.wanBiId != 0)
      {
        wanBi = bagComponent.GetThingInBag<Thing>(req.wanBiId);
        if (wanBi == null || wanBi.num <= 0)
        {
          user.SendToast("完璧宝玉不在背包中");
          return;
        }
        if (wanBi.thingName != ThingNameEnum.WanBi_BaoYu &&
            wanBi.thingName != ThingNameEnum.GaoJi_WanBi &&
            wanBi.thingName != ThingNameEnum.ChuJi_WanBi)
        {
          user.SendToast("该物品不是完璧宝玉");
          return;
        }

        // 检查完璧宝玉等级要求
        if (equipment.level < 30 && wanBi.thingName != ThingNameEnum.ChuJi_WanBi)
        {
          user.SendToast("需要初级完璧宝玉保护强化");
          return;
        }
        if (equipment.level >= 30 && equipment.level < 60 && wanBi.thingName != ThingNameEnum.WanBi_BaoYu)
        {
          user.SendToast("需要完璧宝玉保护强化");
          return;
        }
        if (equipment.level >= 60 && wanBi.thingName != ThingNameEnum.GaoJi_WanBi)
        {
          user.SendToast("需要高级完璧宝玉保护强化");
          return;
        }
      }

      // 检查装备强化条件
      if (equipment.level < 10)
      {
        user.SendToast("10级以上的装备才能强化");
        return;
      }
      if (equipment.enhanceLevel >= 15)
      {
        user.SendToast("该装备强化等级已达上限");
        return;
      }

      // 检查升级石等级要求
      if (equipment.level < 30 && shengJiShi.thingSubType != ThingSubType.ChuJi_ShengJiShi)
      {
        user.SendToast("需要初级升级石强化该装备");
        return;
      }
      if (equipment.level >= 30 && equipment.level < 60 && shengJiShi.thingSubType != ThingSubType.ShengJiShi)
      {
        user.SendToast("需要升级石强化该装备");
        return;
      }
      if (equipment.level >= 60 && shengJiShi.thingSubType != ThingSubType.GaoJi_ShengJiShi)
      {
        user.SendToast("需要高级升级石强化该装备");
        return;
      }

      // 消耗升级石
      bagComponent.AddThingNumWithSend(shengJiShi, -1);

      // 判断强化是否成功
      bool isSuccess = equipment.IsEnhanceSuccess(shengJiShi);

      if (!isSuccess)
      {
        // 强化失败，判断是否掉级
        if (equipment.IsDropLevel())
        {
          if (wanBi != null)
          {
            // 有完璧宝玉保护，消耗完璧宝玉
            bagComponent.AddThingNumWithSend(wanBi, -1);
            user.SendToast("强化失败，完璧宝玉减少1个");
          }
          else
          {
            // 没有保护，装备掉级
            equipment.enhanceLevel--;
            equipment.RecalEquipAttrs();

            ServerUpdateBagMsg updateBagThingsOut = new()
            {
              updateList = new List<Thing> { equipment },
              removeThingIds = new List<long>()
            };
            user.SendMessage(updateBagThingsOut);
            user.SendToast("强化失败，装备掉级");
          }
        }
        else
        {
          user.SendToast("强化失败");
        }
      }
      else
      {
        // 强化成功
        equipment.enhanceLevel++;
        equipment.RecalEquipAttrs();

        ServerUpdateBagMsg updateBagThingsOut = new()
        {
          updateList = new List<Thing> { equipment },
          removeThingIds = new List<long>()
        };
        user.SendMessage(updateBagThingsOut);
        user.SendToast("强化成功");
      }

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class UserRepairEquipMsgHandler : MessageLocationHandler<MapNode, RepairEquipMsg>
  {
    protected override async ETTask Run(MapNode nowMap, RepairEquipMsg req)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(req.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      EquipComponent equipComponent = user.GetComponent<EquipComponent>();
      bool needRecalcUserAttrs = false;
      if (req.isRepairAll)
      {
        // 修理所有装备
        long totalCost = 0;
        List<Equipment> needRepairEquips = new List<Equipment>();

        foreach (Equipment equipment in equipComponent.equipList)
        {
          if (equipment == null || equipment.thingType != ThingType.EQUIP)
          {
            continue;
          }
          if (EquipConstant.IsSpecialEquip(equipment.thingSubType))
          {
            continue;
          }
          long repairCost = equipment.GetRepairCost();
          if (repairCost > 0)
          {
            totalCost += repairCost;
            needRepairEquips.Add(equipment);
          }
        }

        if (totalCost > bagComponent.coin)
        {
          user.SendToast("金币不足");
          return;
        }
        if (totalCost <= 0)
        {
          user.SendToast("您没有可修理的装备");
          return;
        }

        bagComponent.AddAllCoinWithSend(-totalCost);
        foreach (Equipment equipment in needRepairEquips)
        {
          if (equipment.remainUseCnt == 0)
          {
            needRecalcUserAttrs = true;
          }
          equipment.remainUseCnt = equipment.useCnt;
        }
      }
      else
      {
        // 修理单个装备
        Equipment equipment = equipComponent.GetUserEquip(req.equipId);
        if (equipment == null)
        {
          user.SendToast("装备不存在");
          return;
        }
        if (EquipConstant.IsSpecialEquip(equipment.thingSubType))
        {
          user.SendToast("该装备无法修理");
          return;
        }
        if (equipment.remainUseCnt == equipment.useCnt)
        {
          user.SendToast("该装备无需修理");
          return;
        }

        long repairCost = equipment.GetRepairCost();
        if (repairCost > bagComponent.coin)
        {
          user.SendToast("金币不足");
          return;
        }

        bagComponent.AddAllCoinWithSend(-repairCost);
        if (equipment.remainUseCnt == 0)
        {
          needRecalcUserAttrs = true;
        }
        equipment.remainUseCnt = equipment.useCnt;
      }
      ServerSendRepairCostMsg repairCostMsg = new();
      foreach (Equipment equip in equipComponent.equipList)
      {
        if (equip == null || equip.thingType != ThingType.EQUIP || EquipConstant.IsSpecialEquip(equip.thingSubType))
        {
          continue;
        }
        long cost = equip.GetRepairCost();
        if (cost > 0)
        {
          repairCostMsg.repairCostMap.Add(equip.thingId, cost);
        }
      }
      user.SendMessage(repairCostMsg);
      if (needRecalcUserAttrs)
      {
        user.RecalcUserAttrs();
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Base_Attack,
            UserUpdateFlagEnum.Equip_List, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      }
      else
      {
        user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Equip_List));
      }
      user.SendToast("修理装备成功");

      await ETTask.CompletedTask;
    }
  }
}