using System.Collections.Generic;

namespace MaoYouJi
{
  public static class AccountProcSys
  {
    public static async ETTask<Account> GetAccount(this User self)
    {
      int process = self.Root().Fiber.Process;
      List<StartSceneConfig> startSceneConfigs = StartSceneConfigCategory.Instance.ProcessScenes[process];
      StartSceneConfig startSceneConfig = RandomGenerator.RandomArray(startSceneConfigs);
      Fiber fiber = FiberManager.Instance.Get(startSceneConfig.Id);
      if (fiber == null)
      {
        return null;
      }
      // 根据网关账户ID获取本地账号
      GateAccountsComponent gateAccountsComponent = fiber.Root.GetComponent<GateAccountsComponent>();
      Account account = await gateAccountsComponent.GetAccount(self.netAccountId);
      return account;
    }
  }
}