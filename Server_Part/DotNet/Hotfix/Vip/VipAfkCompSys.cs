using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(VipAfkComp))]
  [FriendOf(typeof(VipAfkComp))]
  public static partial class VipAfkCompSys
  {
    [EntitySystem]
    private static void Awake(this VipAfkComp self)
    {
    }

    public static Dictionary<string, string> GetLianGongFangSetting(this VipAfkComp self)
    {
      User user = self.GetParent<User>();
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      SkillComponent skillComponent = user.GetComponent<AttackComponent>().GetComponent<SkillComponent>();
      string skillId = "无";
      if (self.skillId != SkillIdEnum.None)
      {
        Skill skill = skillComponent.GetSkill(self.skillId);
        if (skill != null)
        {
          skillId = skill.name;
        }
      }
      Dictionary<string, string> settingMap = new Dictionary<string, string>();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      settingMap["skillId"] = skillId;
      long remainTotal = self.isGaoji ? vipFuncInfo.remainGaoJiLianGongFangTime
          : vipFuncInfo.remainLianGongFangTime;
      settingMap["remainTotal"] = remainTotal.ToString();
      return settingMap;
    }

    public static Dictionary<string, string> GetLvGuanSetting(this VipAfkComp self)
    {
      User user = self.GetParent<User>();
      UserVipFuncInfo vipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      Dictionary<string, string> settingMap = new Dictionary<string, string>();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      settingMap["existTime"] = self.existTime.ToString();
      settingMap["addZenExp"] = self.addZenExp.ToString();
      long remainTotal = self.isGaoji ? vipFuncInfo.remainGaoJiLvGuanTime
          : vipFuncInfo.remainLvGuanTime;
      settingMap["remainTotal"] = remainTotal.ToString();
      return settingMap;
    }

    public static Dictionary<string, string> GetAfkSetting(this VipAfkComp self)
    {
      Dictionary<string, string> settingMap = new();
      settingMap["remainTime"] = self.remainTime.ToString();
      settingMap["setTime"] = self.setTime.ToString();
      settingMap["enterTime"] = self.enterTime.ToString();
      settingMap["totalExp"] = self.totalExp.ToString();
      return settingMap;
    }
  }
}