using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Attack)]
  public class InnerStartAttackHandler : MessageHandler<Scene, InnerStartAttackReq, InnerStartAttackResp>
  {
    protected override async ETTask Run(Scene scene, InnerStartAttackReq request, InnerStartAttackResp response)
    {
      if (request.attackId == 0)
      {
        ETLog.Error($"战斗ID不能为0: {request.srcFightInfo} {request.targetFightInfo}");
        response.SetError("战斗ID不能为0");
        return;
      }
      AttackComponent srcAttack = AttackProcSys.GetAttackComponent(request.srcFightInfo);
      AttackComponent targetAttack = AttackProcSys.GetAttackComponent(request.targetFightInfo);
      if (srcAttack == null || targetAttack == null)
      {
        ETLog.Error($"战斗对象不存在: {request.srcFightInfo} {request.targetFightInfo}");
        response.SetError("战斗对象不存在");
        return;
      }
      if (srcAttack.LiveState != LiveStateEnum.ALIVE || targetAttack.LiveState != LiveStateEnum.ALIVE)
      {
        ETLog.Warning($"战斗对象状态异常: {srcAttack.LiveState} {targetAttack.LiveState}");
        response.SetError("对方已在战斗中");
        return;
      }
      if (srcAttack.fightInfo.fightId == targetAttack.fightInfo.fightId)
      {
        ETLog.Error($"战斗对象相同: {srcAttack.fightInfo.fightId}");
        response.SetError("无法和自己战斗");
        return;
      }
      if (srcAttack.Parent.GetParent<MapNode>().Id != targetAttack.Parent.GetParent<MapNode>().Id)
      {
        ETLog.Error($"战斗对象不在同一地图: {srcAttack.Parent.GetParent<MapNode>().Id} {targetAttack.Parent.GetParent<MapNode>().Id}");
        response.SetError("战斗对象不在同一地图");
        return;
      }
      scene.AddChildWithId<AttackInCache, AttackComponent, AttackComponent>(request.attackId, srcAttack, targetAttack);
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerJoinAttackHandler : MessageHandler<AttackInCache, InnerJoinAttackReq, InnerJoinAttackResp>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerJoinAttackReq request, InnerJoinAttackResp response)
    {
      if (request.attackId != attackInCache.Id)
      {
        ETLog.Error($"战斗ID不匹配: {request.attackId} {attackInCache.Id}");
        response.SetError("战斗ID不匹配");
        return;
      }
      if (attackInCache.IsEnd)
      {
        ETLog.Error($"战斗不存在: {request.attackId}");
        response.SetError("战斗已结束");
        return;
      }
      AttackComponent srcAttack = AttackProcSys.GetAttackComponent(request.srcFightInfo);
      AttackComponent targetAttack = attackInCache.GetFightTarget(request.targetFightInfo);
      if (srcAttack == null || targetAttack == null)
      {
        ETLog.Error($"战斗对象不存在: {request.srcFightInfo} {request.targetFightInfo}");
        response.SetError("战斗对象不存在");
        return;
      }
      if (srcAttack.LiveState != LiveStateEnum.ALIVE)
      {
        ETLog.Warning($"发起方状态异常: {srcAttack.fightInfo.fightName}-{srcAttack.fightInfo.fightId} {srcAttack.LiveState}");
        response.SetError("您已在战斗中");
        return;
      }
      if (targetAttack.LiveState != LiveStateEnum.FIGHTING)
      {
        ETLog.Warning($"战斗对象: {targetAttack.fightInfo.fightName}-{targetAttack.fightInfo.fightId} 状态异常: {targetAttack.LiveState}");
        response.SetError("对方已退出战斗");
        return;
      }
      if (srcAttack.Parent.GetParent<MapNode>().Id != targetAttack.Parent.GetParent<MapNode>().Id)
      {
        ETLog.Error($"战斗对象不在同一地图: {srcAttack.Parent.GetParent<MapNode>().Id} {targetAttack.Parent.GetParent<MapNode>().Id}");
        response.SetError("战斗对象不在同一地图");
        return;
      }
      attackInCache.AddFight(srcAttack, targetAttack);
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerExecNormalAttackHandler : MessageHandler<AttackInCache, InnerExecNormalAttack>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerExecNormalAttack msg)
    {
      AttackComponent srcAttack = AttackProcSys.GetAttackComponent(msg.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"对象不在战斗中: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      InFightComponent inFightComponent = srcAttack.GetComponent<InFightComponent>();
      if (inFightComponent == null || inFightComponent.attackTarget == null)
      {
        ETLog.Warning($"战斗对象为空: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      NormalAttackComp normalAttackComp = inFightComponent.GetComponent<NormalAttackComp>();
      if (normalAttackComp == null)
      {
        ETLog.Error($"对象没有普通攻击组件: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      if (TimeInfo.Instance.ServerNow() - normalAttackComp.LastNormalAttackTime < 200)
      {
        ETLog.Warning($"普通攻击过于频繁: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      // 咏唱中不执行普通攻击
      if (inFightComponent.GetComponent<AttackSongSkillComp>() != null)
      {
        return;
      }
      normalAttackComp.ExecNormalAttack();
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerUseSkillHandler : MessageHandler<AttackInCache, InnerUseSkillMsg>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerUseSkillMsg msg)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(msg.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      bool isUser = srcAttack.fightInfo.liveType == LiveType.ROLE;
      User user = srcAttack.GetParent<User>();
      InFightComponent inFightComponent = srcAttack.GetComponent<InFightComponent>();
      if (inFightComponent == null)
      {
        user?.SendToast("您不在战斗中！");
        return;
      }
      Skill skill = null;
      if (isUser)
      {
        SkillComponent skillComponent = srcAttack.GetComponent<SkillComponent>();
        skill = skillComponent.GetSkill(msg.skillId, true);
        if (skill == null)
        {
          user?.SendToast("该技能未快捷");
          return;
        }
        inFightComponent.UseSkill(skill);
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerExecEscapeHandler : MessageHandler<AttackInCache, InnerExecEscapeMsg>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerExecEscapeMsg msg)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(msg.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
        return;
      }
      AttackCtxComp attackCtx = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(srcAttack);
      try
      {
        InFightComponent inFightComponent = srcAttack.GetComponent<InFightComponent>();
        if (inFightComponent == null)
        {
          ETLog.Warning($"未在战斗中: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
          return;
        }
        if (!inFightComponent.HasState(AttackState.Tao_Pao))
        {
          ETLog.Warning($"逃跑状态已移除: {msg.fightInfo.fightName}-{msg.fightInfo.fightId}");
          return;
        }
        AttackComponent destAttack = attackInCache.GetFightTarget(inFightComponent.attackTarget);
        long srcLevel = srcAttack.level, destLevel = destAttack.level;
        long randomVal = RandomGenerator.RandomNumber(0, 100);
        // 逃跑成功的概率默认为20%，每级增加5%，最少为5%
        long prob = 30 + (srcLevel - destLevel) * 5;
        ActiveJobInfo activeJobInfo = inFightComponent.GetComponent<ActiveJobInfo>();
        Skill talentSkill = activeJobInfo?.ActiveTalentSkill;
        if (talentSkill != null)
        {
          if (talentSkill.skillId == SkillIdEnum.ShenSheng_JieJie)
          { // 神圣结界增加逃跑概率
            prob += talentSkill.vals[1] / 10; // 千分制的，所以除以10
          }
        }
        if (prob < 5)
        {
          prob = 5;
        }
        // 狂风笼罩和暴怒状态下逃跑概率为0
        if (inFightComponent.HasState(AttackState.KuangFeng_LongZhao) || inFightComponent.HasState(AttackState.Bao_Nu))
        {
          prob = 0;
        }
        bool success = randomVal < prob;
        if (srcAttack.fightInfo.liveType == LiveType.ROLE)
        {
          srcAttack.GetParent<User>().SendToast(success ? "逃跑成功！" : "逃跑失败！");
        }
        attackInCache.SendEscapeInfo(srcAttack, success);
        if (success)
        {
          // 逃跑成功后，退出战斗
          AttackFlowNode rootNode = attackCtx.BuildQuitAttackFlow();
          rootNode.ExecuteTree();
        }
        else
        {
          // 准备设置状态的上下文，并提交到流程节点
          SetStateCtx setStateCtx = new SetStateCtx(srcAttack, false, null, AttackState.Tao_Pao);
          setStateCtx.isAutoRemove = true;
          AttackFlowNode setStateNode = AttackFlowNodeFunc.CreateNode(AttackFlowEnum.Set_State, setStateCtx);
          setStateNode.AddChild(AttackFlowEnum.Proc_Rlt, null);
          // 开始执行工作流
          setStateNode.ExecuteTree();
        }
        await ETTask.CompletedTask;
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerUseThingHandler : MessageHandler<AttackInCache, InnerUseThingReq, InnerUseThingResp>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerUseThingReq request, InnerUseThingResp response)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(request.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {request.fightInfo.fightName}-{request.fightInfo.fightId}");
        response.SetError("战斗对象不存在");
        return;
      }
      AttackCtxComp attackCtx = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(srcAttack);
      try
      {
        AttackFlowNode rootNode = attackCtx.BuildUseThingFlow(request.thing);
        rootNode.ExecuteTree();
      }
      catch (Exception e)
      {
        ETLog.Error($"使用物品失败: {request.fightInfo.fightName}-{request.fightInfo.fightId} {e}");
        response.SetError("使用物品失败");
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerQuitAttackHandler : MessageHandler<AttackInCache, InnerQuitAttackReq, InnerQuitAttackResp>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerQuitAttackReq request, InnerQuitAttackResp response)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(request.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {request.fightInfo.fightName}-{request.fightInfo.fightId}");
        response.SetError("战斗对象不存在");
        return;
      }
      AttackCtxComp attackCtx = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(srcAttack);
      try
      {
        AttackFlowNode rootNode = attackCtx.BuildQuitAttackFlow();
        rootNode.ExecuteTree();
      }
      catch (Exception e)
      {
        ETLog.Error($"退出战斗失败: {request.fightInfo.fightName}-{request.fightInfo.fightId} {e}");
        response.SetError("退出战斗失败");
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageHandler(SceneType.Attack)]
  public class InnerUserJinChanTuoQiaoHandler : MessageHandler<AttackInCache, InnerUserJinChanTuoQiaoReq, InnerUserJinChanTuoQiaoResp>
  {
    protected override async ETTask Run(AttackInCache attackInCache, InnerUserJinChanTuoQiaoReq request, InnerUserJinChanTuoQiaoResp response)
    {
      AttackComponent srcAttack = attackInCache.GetFightTarget(request.fightInfo);
      if (srcAttack == null)
      {
        ETLog.Warning($"战斗对象不存在: {request.fightInfo.fightName}-{request.fightInfo.fightId}");
        response.SetError("战斗对象不存在");
        return;
      }
      AttackCtxComp attackCtx = attackInCache.AddComponent<AttackCtxComp, AttackComponent>(srcAttack);
      try
      {
        AttackFlowNode rootNode = attackCtx.BuildJinChanTuoQiaoFlow(request.thing);
        LogicRet rlt = rootNode.ExecuteTree();
        if (!rlt.IsSuccess)
        {
          ETLog.Error($"使用金蝉脱壳失败: {request.fightInfo.fightName}-{request.fightInfo.fightId} {rlt.Message}");
          response.SetError("使用金蝉脱壳失败");
          return;
        }
      }
      catch (Exception e)
      {
        ETLog.Error($"使用金蝉脱壳失败: {request.fightInfo.fightName}-{request.fightInfo.fightId} {e}");
        response.SetError("使用金蝉脱壳失败");
      }
      finally
      {
        attackInCache.RemoveComponent<AttackCtxComp>();
      }
      await ETTask.CompletedTask;
    }
  }
}