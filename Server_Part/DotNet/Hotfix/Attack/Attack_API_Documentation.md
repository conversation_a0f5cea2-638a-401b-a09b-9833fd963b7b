# Attack模块接口文档

## 概述
Attack模块是游戏战斗系统的核心模块，负责处理所有与战斗相关的逻辑，包括战斗发起、技能释放、伤害计算、状态管理、逃跑等功能。该模块采用ECS架构设计，通过工作流模式处理复杂的战斗逻辑。

## 目录结构
```
Attack/
├── AttackComponentSystem.cs          # 攻击组件核心系统
├── AttackProcSys.cs                 # 攻击处理工具类
├── AttackWorkFlow/                   # 战斗工作流模块
│   ├── AttackFlowBuilder.cs         # 工作流构建器
│   ├── AttackFlowHanlder.cs         # 工作流处理器
│   ├── AttackFlowNodeFunc.cs        # 工作流节点功能
│   ├── SkillInjectInvoker.cs        # 技能注入调用器
│   ├── MonSkillInjectInvoker.cs     # 怪物技能注入调用器
│   ├── SelfStateInjectInvoker.cs    # 自身状态注入调用器
│   └── TargetStateInjectInvoker.cs  # 目标状态注入调用器
├── InFightCompSys/                  # 战斗中组件系统
│   ├── InFightComponentSystem.cs    # 战斗中组件核心系统
│   ├── UpdateAttackSystem.cs        # 攻击更新系统
│   ├── AttackStatusComponentSystem.cs # 攻击状态组件系统
│   ├── KilledComponentSystem.cs     # 击杀组件系统
│   ├── NormalAttackCompSys.cs       # 普通攻击组件系统
│   ├── ActiveJobInfoSystem.cs       # 激活职业信息系统
│   ├── InFightAttrSystem.cs         # 战斗中属性系统
│   └── AttackSongSkillCompSys.cs    # 攻击咏唱技能组件系统
├── AttackMsgHandlers/               # 攻击消息处理器
│   ├── AttackOptMsgHandler.cs       # 攻击操作消息处理器
│   └── AttackInnerMsgHandler.cs     # 攻击内部消息处理器
└── AttackInCacheSys/               # 攻击缓存系统
    ├── AttackInCacheSys.cs         # 攻击缓存核心系统
    └── AttackCtxCompSys.cs         # 攻击上下文组件系统
```

---

## 1. 核心组件系统 (AttackComponentSystem)

### 1.1 Load
```csharp
public static void Load(this AttackComponent self)
```
**功能**: 加载攻击组件，移除战斗中组件
**使用场景**: 组件初始化时调用
**参数**: 无

### 1.2 OutUseFuJi (伏击技能)
```csharp
public static async ETTask<LogicRet> OutUseFuJi(this AttackComponent self, Skill skill, AttackComponent target)
```
**功能**: 在非战斗状态下使用伏击技能
**使用场景**: 玩家在地图上对其他玩家或怪物使用伏击技能
**参数**:
- `skill`: 伏击技能对象
- `target`: 攻击目标
**返回值**: LogicRet - 操作结果
**限制条件**:
- 技能必须冷却完成
- 目标不能为空
- 蓝量必须足够
- 必须激活对应职业
- 必须装备匕首且耐久度大于0

### 1.3 GetPos (获取位置)
```csharp
public static void GetPos(this AttackComponent self, out string mapName, out string pointName)
```
**功能**: 获取攻击组件所在的地图和位置信息
**使用场景**: 需要确定战斗对象位置时
**参数**:
- `mapName`: 输出地图名称
- `pointName`: 输出位置点名称

### 1.4 UpdateMapInfo (更新地图信息)
```csharp
public static void UpdateMapInfo(this AttackComponent self)
```
**功能**: 更新对象在地图节点中的信息
**使用场景**: 战斗状态变化时同步地图信息

### 1.5 SetUserAttack (设置用户攻击属性)
```csharp
public static void SetUserAttack(this AttackComponent self, BaseAttack baseAttack)
```
**功能**: 根据基础攻击属性设置用户的战斗属性
**使用场景**: 用户属性变化或进入战斗时
**参数**: `baseAttack` - 基础攻击属性对象
**计算规则**:
- 战士职业: 攻击力基于力量属性，生命值=基础+力量×12
- 法师职业: 攻击力基于精神属性，蓝量=基础+智力×12

### 1.6 CalcAttackNum (计算战斗力)
```csharp
public static long CalcAttackNum(this AttackComponent self)
```
**功能**: 计算综合战斗力数值
**使用场景**: 战斗力排行、匹配等
**返回值**: long - 综合战斗力数值
**计算因子**: 攻击力、暴击、命中、闪避、防御、生命值、蓝量等

---

## 2. 攻击处理工具类 (AttackProcSys)

### 2.1 GetAttackComponent
```csharp
public static AttackComponent GetAttackComponent(FightInfo fightInfo)
```
**功能**: 根据战斗信息获取对应的攻击组件
**使用场景**: 消息处理、战斗逻辑中需要获取攻击对象时
**参数**: `fightInfo` - 战斗信息对象
**返回值**: AttackComponent - 攻击组件，不存在时返回null

---

## 3. 战斗工作流系统 (AttackWorkFlow)

### 3.1 AttackFlowBuilder (工作流构建器)

#### 3.1.1 InjectFlow (注入工作流)
```csharp
public static void InjectFlow(this AttackCtxComp ctx, AttackFlowNode root)
```
**功能**: 向工作流注入技能、状态等特殊处理逻辑
**使用场景**: 构建攻击工作流时注入各种拦截器
**参数**:
- `ctx`: 攻击上下文组件
- `root`: 根工作流节点

#### 3.1.2 BuildAutoAttackFlow (构建自动攻击工作流)
```csharp
public static AttackFlowNode BuildAutoAttackFlow(this AttackCtxComp ctx)
```
**功能**: 构建普通攻击的工作流
**使用场景**: 执行普通攻击时
**返回值**: AttackFlowNode - 工作流根节点
**工作流程**: 前置条件检查 → 预处理 → 命中判断 → 伤害计算 → 结果处理

#### 3.1.3 BuildDmgSkillFlow (构建伤害技能工作流)
```csharp
public static AttackFlowNode BuildDmgSkillFlow(this AttackCtxComp ctx, Skill skill)
```
**功能**: 构建造成伤害的技能工作流
**使用场景**: 释放攻击性技能时
**参数**: `skill` - 技能对象
**返回值**: AttackFlowNode - 工作流根节点

#### 3.1.4 BuildCureSkillFlow (构建治疗技能工作流)
```csharp
public static AttackFlowNode BuildCureSkillFlow(this AttackCtxComp ctx, Skill skill)
```
**功能**: 构建治疗技能的工作流
**使用场景**: 释放治疗技能时
**参数**: `skill` - 技能对象
**返回值**: AttackFlowNode - 工作流根节点

#### 3.1.5 BuildEscapeFlow (构建逃跑工作流)
```csharp
public static AttackFlowNode BuildEscapeFlow(this AttackCtxComp ctx)
```
**功能**: 构建逃跑操作的工作流
**使用场景**: 玩家选择逃跑时
**返回值**: AttackFlowNode - 工作流根节点

### 3.2 AttackFlowHandler (工作流处理器)

#### 3.2.1 CheckPreCondHandler (前置条件检查处理器)
**功能**: 检查攻击前置条件
**检查项目**:
- 自身状态限制 (眩晕、冰冻、缠绕等)
- 目标状态要求
- 蓝量消耗检查
- 自定义条件检查

#### 3.2.2 PreProcHandler (预处理处理器)
**功能**: 攻击前的预处理逻辑
**处理内容**:
- 移除特定状态
- 处理技能冷却
- 消耗蓝量
- 消耗物品

#### 3.2.3 IsHitHandler (命中判断处理器)
**功能**: 判断攻击是否命中目标
**计算因子**: 命中率、闪避率、等级差异等

#### 3.2.4 CalcDmgHandler (伤害计算处理器)
**功能**: 计算攻击造成的伤害
**计算类型**:
- 物理伤害
- 魔法伤害
- 百分比伤害
- 固定伤害

#### 3.2.5 DmgTargetHandler (伤害目标处理器)
**功能**: 对目标造成伤害
**处理内容**:
- 扣除目标生命值
- 判断是否死亡
- 触发相关事件

#### 3.2.6 SetStateHandler (状态设置处理器)
**功能**: 添加或移除战斗状态
**支持操作**:
- 添加状态 (眩晕、中毒、增益等)
- 移除状态
- 状态层数管理

---

## 4. 战斗中组件系统 (InFightCompSys)

### 4.1 InFightComponentSystem (战斗中组件核心系统)

#### 4.1.1 Awake (初始化)
```csharp
private static void Awake(this InFightComponent self, AttackComponent target, AttackInCache attackInCache)
```
**功能**: 初始化战斗中组件
**使用场景**: 对象进入战斗时
**参数**:
- `target`: 攻击目标
- `attackInCache`: 战斗缓存对象

#### 4.1.2 AddFightTarget (添加战斗目标)
```csharp
public static void AddFightTarget(this InFightComponent self, AttackComponent target)
```
**功能**: 向战斗列表中添加新的战斗目标
**使用场景**: 多人战斗时有新对象加入

#### 4.1.3 HasState (检查状态)
```csharp
public static bool HasState(this InFightComponent self, AttackState state)
```
**功能**: 检查是否拥有指定状态
**使用场景**: 技能释放前的状态检查
**参数**: `state` - 攻击状态枚举
**返回值**: bool - 是否拥有该状态

#### 4.1.4 UseSkill (使用技能)
```csharp
public static LogicRet UseSkill(this InFightComponent self, Skill skill)
```
**功能**: 在战斗中使用技能
**使用场景**: 战斗中释放技能
**参数**: `skill` - 技能对象
**返回值**: LogicRet - 操作结果
**检查项目**:
- 职业匹配
- 武器要求
- 技能冷却
- 状态限制

#### 4.1.5 StartEscape (开始逃跑)
```csharp
public static LogicRet StartEscape(this InFightComponent self)
```
**功能**: 开始逃跑操作
**使用场景**: 玩家选择逃跑时
**返回值**: LogicRet - 操作结果
**限制条件**: 不能在特定状态下逃跑 (眩晕、冰冻等)

---

## 5. 消息处理器 (AttackMsgHandlers)

### 5.1 AttackOptMsgHandler (攻击操作消息处理器)

#### 5.1.1 UserStartAttackHandler (开始战斗处理器)
```csharp
[MessageHandler(SceneType.Map)]
public class UserStartAttackHandler : MessageLocationHandler<MapNode, ClientStartAttackMsg>
```
**功能**: 处理客户端发起战斗的请求
**消息类型**: ClientStartAttackMsg
**场景类型**: Map (地图场景)
**处理流程**:
1. 验证用户状态
2. 获取攻击目标
3. 调用地图攻击管理器开始战斗

#### 5.1.2 UserUseSkillHandler (使用技能处理器)
```csharp
[MessageHandler(SceneType.MapAndAttack)]
public class UserUseSkillHandler : MessageLocationHandler<Entity, ClientUseSkillMsg>
```
**功能**: 处理客户端使用技能的请求
**消息类型**: ClientUseSkillMsg
**场景类型**: MapAndAttack (地图和战斗场景)
**特殊处理**: 支持非战斗状态下的伏击技能

#### 5.1.3 UserEscapeHandler (逃跑处理器)
```csharp
[MessageHandler(SceneType.Attack)]
public class UserEscapeHandler : MessageHandler<AttackInCache, ClientEscapeMsg>
```
**功能**: 处理客户端逃跑请求
**消息类型**: ClientEscapeMsg
**场景类型**: Attack (战斗场景)

### 5.2 AttackInnerMsgHandler (攻击内部消息处理器)

#### 5.2.1 InnerStartAttackHandler (内部开始战斗处理器)
**功能**: 处理内部战斗开始请求
**验证项目**:
- 战斗对象存在性
- 战斗对象状态
- 地图一致性
- 战斗ID有效性

#### 5.2.2 InnerJoinAttackHandler (内部加入战斗处理器)
**功能**: 处理对象加入现有战斗的请求
**使用场景**: 多人战斗时新对象加入

#### 5.2.3 InnerExecNormalAttackHandler (内部执行普通攻击处理器)
**功能**: 处理普通攻击的执行
**频率限制**: 200ms内不能重复攻击
**状态检查**: 咏唱中不执行普通攻击

#### 5.2.4 InnerUseSkillHandler (内部使用技能处理器)
**功能**: 处理战斗中技能使用的内部逻辑
**验证项目**: 技能快捷栏配置、战斗状态等

#### 5.2.5 InnerExecEscapeHandler (内部执行逃跑处理器)
**功能**: 处理逃跑的具体执行逻辑
**成功率计算**: 基础30% + (等级差×5%)，最低5%
**特殊加成**: 神圣结界天赋技能可增加逃跑概率

---

## 6. 攻击缓存系统 (AttackInCacheSys)

### 6.1 AttackInCacheSys (攻击缓存核心系统)

#### 6.1.1 Awake (初始化战斗缓存)
```csharp
private static void Awake(this AttackInCache self, AttackComponent src, AttackComponent target)
```
**功能**: 初始化战斗缓存对象
**使用场景**: 创建新的战斗实例时
**初始化内容**:
- 设置战斗参与者
- 记录战斗开始时间
- 添加邮箱组件用于消息接收
- 注册到全局缓存

#### 6.1.2 AddFight (添加战斗)
```csharp
public static void AddFight(this AttackInCache self, AttackComponent src, AttackComponent target)
```
**功能**: 向现有战斗中添加新的参与者
**使用场景**: 多人战斗扩展

#### 6.1.3 EndAttack (结束战斗)
```csharp
public static void EndAttack(this AttackInCache self)
```
**功能**: 结束战斗并清理资源
**使用场景**: 战斗结束时
**清理内容**:
- 移除所有参与者的战斗组件
- 清空战斗列表
- 延迟销毁战斗缓存对象

#### 6.1.4 GetFightTarget (获取战斗目标)
```csharp
public static AttackComponent GetFightTarget(this AttackInCache self, FightInfo fightInfo)
public static AttackComponent GetFightTarget(this AttackInCache self, long fightId)
```
**功能**: 根据战斗信息或ID获取战斗目标
**使用场景**: 战斗逻辑中需要获取特定目标时
**返回值**: AttackComponent - 目标对象，不存在时返回null

#### 6.1.5 GetTargetList (获取目标列表)
```csharp
public static List<AttackComponent> GetTargetList(this AttackInCache self, AttackComponent src)
```
**功能**: 获取指定对象的所有战斗目标列表
**使用场景**: 群体技能、范围攻击等
**返回值**: List<AttackComponent> - 目标列表

---

## 7. 使用示例

### 7.1 发起战斗
```csharp
// 在地图管理器中发起战斗
MapAttackManage mapAttackManage = mapNode.GetComponent<MapAttackManage>();
AttackComponent attacker = user.GetComponent<AttackComponent>();
AttackComponent target = targetUser.GetComponent<AttackComponent>();
LogicRet result = await mapAttackManage.StartAttackBase(attacker, target);
```

### 7.2 使用技能
```csharp
// 在战斗中使用技能
InFightComponent inFight = attacker.GetComponent<InFightComponent>();
Skill skill = skillComponent.GetSkill(skillId);
LogicRet result = inFight.UseSkill(skill);
```

### 7.3 检查战斗状态
```csharp
// 检查是否拥有特定状态
InFightComponent inFight = attacker.GetComponent<InFightComponent>();
bool hasStun = inFight.HasState(AttackState.DIZZY);
```

### 7.4 逃跑操作
```csharp
// 开始逃跑
InFightComponent inFight = attacker.GetComponent<InFightComponent>();
LogicRet result = inFight.StartEscape();
```

---

## 8. 注意事项

### 8.1 线程安全
- 所有战斗逻辑都在单一Fiber中执行，避免并发问题
- 使用ConcurrentDictionary存储战斗列表确保线程安全

### 8.2 性能优化
- 工作流采用节点复用机制
- 状态检查使用快速查找
- 及时清理无用的技能冷却记录

### 8.3 错误处理
- 所有公共接口都返回LogicRet进行错误处理
- 关键操作都有日志记录
- 异常情况下自动清理资源

### 8.4 扩展性
- 工作流支持动态注入自定义逻辑
- 状态系统支持自定义状态类型
- 技能系统支持复杂的技能效果组合

---

## 9. 相关枚举和常量

### 9.1 AttackState (攻击状态)
- `DIZZY`: 眩晕状态
- `FROZEN`: 冰冻状态
- `ChanRao`: 缠绕状态
- `SILENT`: 沉默状态
- `Tao_Pao`: 逃跑状态
- `ADD_NORMAL`: 普通攻击增强
- `ADD_MAGIC`: 魔法攻击增强

### 9.2 AttackType (攻击类型)
- `Normal`: 普通攻击
- `Skill`: 技能攻击

### 9.3 LiveStateEnum (生存状态)
- `ALIVE`: 存活状态
- `FIGHTING`: 战斗状态
- `DEAD`: 死亡状态

### 9.4 DmgType (伤害类型)
- `Physics`: 物理伤害
- `Magic`: 魔法伤害

---

本文档涵盖了Attack模块的所有主要接口和功能，为开发者提供了完整的API参考和使用指南。如需了解更多细节，请参考具体的源代码实现。 