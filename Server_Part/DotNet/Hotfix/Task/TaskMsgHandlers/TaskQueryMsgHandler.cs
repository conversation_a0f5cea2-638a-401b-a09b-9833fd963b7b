using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GetBiaoCheTaskHandler : MessageLocationHandler<MapNode, GetBiaoCheTaskReq, GetBiaoCheTaskResp>
  {
    protected override async ETTask Run(MapNode nowMap, GetBiaoCheTaskReq msg, GetBiaoCheTaskResp resp)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      List<BaseTask> tasks = taskComponent.GetBaseTasksByStartNpc(msg.npcName, TaskTypeEnum.Daily_Task, TaskSubTypeEnum.BiaoChe_Task);
      if (tasks.Count <= 0)
      {
        resp.SetError("没找到镖车任务");
        return;
      }
      resp.tasks = tasks;
      UserDailyCntInfo userDailyCntInfo = user.GetComponent<UserDailyCntInfo>();
      resp.nowCnt = userDailyCntInfo.biaoCheCnt;
      resp.maxCnt = 10;
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class GetShiMingTaskHandler : MessageLocationHandler<MapNode, GetShiMingTaskReq, GetShiMingTaskResp>
  {
    protected override async ETTask Run(MapNode nowMap, GetShiMingTaskReq msg, GetShiMingTaskResp resp)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        resp.SetError(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      List<BaseTask> tasks = taskComponent.GetBaseTasksByStartNpc(msg.npcName, TaskTypeEnum.Daily_Task, TaskSubTypeEnum.ShiMing_Task);
      if (tasks.Count <= 0)
      {
        resp.SetError("没找到使命任务");
        return;
      }
      resp.tasks = tasks;
      UserDailyCntInfo userDailyCntInfo = user.GetComponent<UserDailyCntInfo>();
      resp.nowCnt = userDailyCntInfo.shiMingCnt;
      resp.maxCnt = 20;
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientGetBiaoChePosHandler : MessageLocationHandler<MapNode, ClientGetBiaoChePosMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientGetBiaoChePosMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, false, false);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();
      BiaoCheInfoComp biaoCheInfoComp = taskComponent.GetComponent<BiaoCheInfoComp>();
      if (biaoCheInfoComp == null)
      {
        user.SendToast("没找到押镖任务");
        return;
      }
      MonsterInfo monsterInfo = biaoCheInfoComp.monsterInfo;
      if (monsterInfo == null || monsterInfo.IsDisposed)
      {
        user.SendToast("未找到镖车");
        return;
      }
      MoveComponent moveComponent = monsterInfo.GetComponent<MoveComponent>();
      if (moveComponent == null)
      {
        user.SendToast("未找到镖车");
        return;
      }
      user.SendToast($"您的镖车在: {moveComponent.nowMap + "/" + moveComponent.nowPoint}");
      user.SendChat($"您的镖车在: {moveComponent.nowMap + "/" + moveComponent.nowPoint}");
      await ETTask.CompletedTask;
    }
  }
}