namespace MaoYouJi
{
  public struct GetThingEvent
  {
    public User user;
    public Thing thing;
    public long num;
    public ThingFromType fromType;
  }

  public struct UseMainTypeThingStruct
  {
    public User user;
    public Thing thing;
    public ClientUseThingMsg msg;
    public MapNode map;
  }

  public struct UseSubTypeThingStruct
  {
    public User user;
    public Thing thing;
    public ClientUseThingMsg msg;
    public MapNode map;
  }

  public struct UseThingStruct
  {
    public User user;
    public Thing thing;
    public ClientUseThingMsg msg;
    public MapNode map;
  }
}