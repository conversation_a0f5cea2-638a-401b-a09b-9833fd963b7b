namespace MaoYouJi
{
  // 从20201开始，避免和系统预留的冲突了
  public static class MaoInnerMsgRange
  {
    // 地图消息 20201-20700,
    public const ushort InnerMoveUserReq = 20201; // 移动用户请求
    public const ushort InnerMoveUserResp = 20202; // 移动用户响应
    // 活动消息，20701-21500
    public const ushort InnerUserQuitDaTaoSha = 21001; // 用户退出大逃杀
    // 战斗消息，21501-22000
    public const ushort InnerStartAttackReq = 21501; // 开始战斗请求
    public const ushort InnerStartAttackResp = 21502; // 开始战斗响应
    public const ushort InnerJoinAttackReq = 21503; // 加入战斗请求
    public const ushort InnerJoinAttackResp = 21504; // 加入战斗响应
    public const ushort InnerExecNormalAttack = 21505; // 执行普通攻击
    public const ushort InnerUseSkillMsg = 21506; // 使用技能
    public const ushort InnerExecEscapeMsg = 21507; // 执行逃跑
    public const ushort InnerUseThingReq = 21508; // 使用物品请求
    public const ushort InnerUseThingResp = 21509; // 使用物品响应
    public const ushort InnerQuitAttackReq = 21510; // 退出战斗请求
    public const ushort InnerQuitAttackResp = 21511; // 退出战斗响应
    public const ushort InnerUserJinChanTuoQiaoReq = 21512; // 用户金蝉脱壳请求
    public const ushort InnerUserJinChanTuoQiaoResp = 21513; // 用户金蝉脱壳响应
    // 关系消息，22001-22500
    public const ushort InnerInitUserFriendInfoMsg = 22001; // 初始化用户好友信息
    public const ushort InnerUnInitUserFriendInfoMsg = 22002; // 卸载用户好友信息
    // 全局消息，22501-23000
    public const ushort InnerGlobalTimerPer_1_MinMsg = 22501; // 全局1分钟定时器消息


  }
}
