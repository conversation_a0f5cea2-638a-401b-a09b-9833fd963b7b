using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON><PERSON>
{
  // 地图内网响应
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerMoveUserResp)]
  public partial class InnerMoveUserResp : MaoYouOutMessage, IResponse
  {
  }

  // 战斗内网响应
  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerStartAttackResp)]
  public partial class InnerStartAttackResp : <PERSON>YouOutMessage, IResponse
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerJoinAttackResp)]
  public partial class InnerJoinAttackResp : MaoYouOutMessage, IResponse
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerUseThingResp)]
  public partial class InnerUseThingResp : MaoYouOutMessage, IResponse
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerQuitAttackResp)]
  public partial class InnerQuitAttackResp : <PERSON>YouOutMessage, IResponse
  {
  }

  [MemoryPackable]
  [Message(MaoInnerMsgRange.InnerUserJinChanTuoQiaoResp)]
  public partial class InnerUserJinChanTuoQiaoResp : <PERSON>YouOutMessage, IResponse
  {
  }
}