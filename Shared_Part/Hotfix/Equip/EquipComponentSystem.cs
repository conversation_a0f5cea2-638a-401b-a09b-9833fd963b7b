using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(EquipComponent))]
  [FriendOf(typeof(EquipComponent))]
  public static partial class EquipComponentSystem
  {
    [EntitySystem]
    private static void Awake(this EquipComponent self)
    {
      self.equipList = new List<Equipment>(); // 装备列表
      self.specialEquipList = new List<Thing>(); // 特殊装备列表
    }

    public static long GetRepairCost(this Equipment equipment)
    {
      if (equipment.useCnt == equipment.remainUseCnt)
      {
        return 0;
      }
      double repairCost = (equipment.useCnt - equipment.remainUseCnt) * equipment.salePrice / equipment.useCnt / 3;
      if (repairCost <= 1e-6)
      {
        return 1;
      }
      return (long)repairCost;
    }

    public static Equipment GetUserEquip(this EquipComponent self, long equipId)
    {
      foreach (Equipment equipment in self.equipList)
      {
        if (equipment.thingId == equipId)
        {
          return equipment;
        }
      }
      return null;
    }


    public static int GetUserEquip(this EquipComponent self, EquipPart equipPart)
    {
      int idx = 0;
      foreach (Equipment equipment in self.equipList)
      {
        if (equipment.equipPart == equipPart)
        {
          return idx;
        }
        ++idx;
      }
      return -1;
    }

    public static int GetUserSpecialEquipIdx(this EquipComponent self, ThingSubType thingSubType)
    {
      int idx = 0;
      foreach (Thing thing in self.specialEquipList)
      {
        if (thing.thingSubType == thingSubType)
        {
          return idx;
        }
        ++idx;
      }
      return -1;
    }

    public static Thing GetUserSpecialEquip(this EquipComponent self, ThingSubType thingSubType)
    {
      int idx = self.GetUserSpecialEquipIdx(thingSubType);
      if (idx == -1)
      {
        return null;
      }
      return self.specialEquipList[idx];
    }


    public static void RecalEquipAttrs(this Equipment equipment)
    {
      EquipAttrInfo attrInfo = equipment.baseAttrInfo, nowAttrInfo = equipment.baseAttrInfo.Clone() as EquipAttrInfo;
      long addPercent = equipment.GetEnhanceAddPercent();
      EquipMainAttrType[] attrTypes = equipment.GetEquipMainAndSubAttr();
      EquipMainAttrType mainAttr = attrTypes[0];
      EquipEnhanceSystem.AddEnhanceAttr(nowAttrInfo, attrInfo, mainAttr, addPercent);
      if (attrTypes[1] != EquipMainAttrType.None)
      {
        EquipEnhanceSystem.AddEnhanceAttr(nowAttrInfo, attrInfo, attrTypes[1], addPercent / 2);
      }
      foreach (Material gem in equipment.gemList)
      {
        if (gem != null)
        {
          // AddGemAttrs(nowAttrInfo, gem);
        }
      }
      equipment.nowAttrInfo = nowAttrInfo;
    }
  }
}
