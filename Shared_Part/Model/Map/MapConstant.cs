using System.Collections.Generic;

namespace MaoYouJi
{
  public static class MapConstant
  {
    [StaticField]
    public static readonly int NoStepLimit = 10000;
    [StaticField]
    public static readonly HashSet<string> canNotUseFastTransferMap = new()
    {
      MapNameConstant.DaTao<PERSON>ha<PERSON>ao,
      MapNameConstant.XiaoHeiWu,
      MapNameConstant.LianGongFang,
      MapNameConstant.LvGuan,
      MapNameConstant.GaoJiLianGongFang,
      MapNameConstant.GaoJiLvGuan,
      MapNameConstant.ChanFang,
      MapNameConstant.DaoGuan,
      MapNameConstant.GongChang
    };
    // 地图相邻地图
    [StaticField]
    public static Dictionary<string, List<string>> mapNearMaps = new()
    {
      { MapNameConstant.MaoYinCun, new List<string> { MapNameConstant.MaoYinXiCun, MapNameConstant.MuYeCaoYuan } },
      { MapNameConstant.MaoYinXiCun, new List<string> { MapNameConstant.MaoYinCun, MapNameConstant.MuYeCaoYuan } },
      { MapNameConstant.MuYeCaoYuan, new List<string> { MapNameConstant.MaoYinCun, MapNameConstant.WanMaCaoYuan, MapNameConstant.DiDaiLinDi } },
      { MapNameConstant.DiDaiLinDi, new List<string> { MapNameConstant.MuYeCaoYuan, MapNameConstant.WuYingDao, MapNameConstant.KeSiTePingYuan } },
      { MapNameConstant.WuYingDao, new List<string> { MapNameConstant.DiDaiLinDi } },
      { MapNameConstant.WanMaCaoYuan, new List<string> { MapNameConstant.MuYeCaoYuan, MapNameConstant.HanFengGu } },
      { MapNameConstant.KeSiTePingYuan, new List<string> { MapNameConstant.DiDaiLinDi, MapNameConstant.TuoBaCheng, MapNameConstant.YinGuangSenLin, MapNameConstant.MaWangTan } }, // 拖把城北、南、西
      { MapNameConstant.TuoBaCheng, new List<string> { MapNameConstant.KeSiTePingYuan } }, // 卡斯特平原中
      { MapNameConstant.HanFengGu, new List<string> { MapNameConstant.WanMaCaoYuan, MapNameConstant.XueSongLinYuan, MapNameConstant.BingFengCheng } },
      { MapNameConstant.XueSongLinYuan, new List<string> { MapNameConstant.HanFengGu, MapNameConstant.DaXueYuan } },
      { MapNameConstant.DaXueYuan, new List<string> { MapNameConstant.BingFengCheng, MapNameConstant.XueSongLinYuan, MapNameConstant.BingHeLieGu } },
      { MapNameConstant.BingFengCheng, new List<string> { MapNameConstant.DaXueYuan, MapNameConstant.HanFengGu, MapNameConstant.BingFengBuBu } }, // 霜雪谷
      { MapNameConstant.BingHeLieGu, new List<string> { MapNameConstant.DaXueYuan, MapNameConstant.DongTuTaiYuan, MapNameConstant.BingFengBuBu } },
      { MapNameConstant.BingFengBuBu, new List<string> { MapNameConstant.BingHeLieGu, MapNameConstant.BingFengWan } },
      { MapNameConstant.YinGuangSenLin, new List<string> { MapNameConstant.KeSiTePingYuan, MapNameConstant.BingFengWan, MapNameConstant.YinXuePingYuan } },
      { MapNameConstant.BingFengWan, new List<string> { MapNameConstant.YinGuangSenLin, MapNameConstant.BingFengBuBu } },
      { MapNameConstant.YinXuePingYuan, new List<string> { MapNameConstant.YinGuangSenLin, MapNameConstant.XueYuanCheng } },
      { MapNameConstant.XueYuanCheng, new List<string> { MapNameConstant.YinXuePingYuan, MapNameConstant.DongTuTaiYuan } },
      { MapNameConstant.DongTuTaiYuan, new List<string> { MapNameConstant.XueYuanCheng, MapNameConstant.BingHeLieGu } },
      { MapNameConstant.MaWangTan, new List<string> { MapNameConstant.KeSiTePingYuan, MapNameConstant.LuWeiDang, MapNameConstant.WuMingHu } },
      { MapNameConstant.LuWeiDang, new List<string> { MapNameConstant.MaWangTan, MapNameConstant.BaiMaGang } },
      { MapNameConstant.WuMingHu, new List<string> { MapNameConstant.BaiMaGang, MapNameConstant.MaWangTan } },
      { MapNameConstant.BaiMaGang, new List<string> { MapNameConstant.WuMingHu, MapNameConstant.LuWeiDang, MapNameConstant.BaiMaHeKou } },
      { MapNameConstant.BaiMaHeKou, new List<string> { MapNameConstant.BaiMaGang, MapNameConstant.JinShaWan } },
      { MapNameConstant.JinShaWan, new List<string> { MapNameConstant.XiSaiPingYuan, MapNameConstant.BaiMaHeKou } },
      { MapNameConstant.XiSaiPingYuan, new List<string> { MapNameConstant.JinShaWan, MapNameConstant.LongJiShan } },
      { MapNameConstant.LongJiShan, new List<string> { MapNameConstant.XiSaiPingYuan, MapNameConstant.LingHunShiJie, MapNameConstant.MaoXiongSenLin, MapNameConstant.LongHeHeGu } },
      { MapNameConstant.LingHunShiJie, new List<string> { MapNameConstant.LongJiShan, MapNameConstant.LingHunCheng } }, // 精灵城内
      { MapNameConstant.LingHunCheng, new List<string> { MapNameConstant.LingHunShiJie } }, // 精灵城内小道
      { MapNameConstant.MaoXiongSenLin, new List<string> { MapNameConstant.LongJiShan, MapNameConstant.QingQuanHu } },
      { MapNameConstant.QingQuanHu, new List<string> { MapNameConstant.MaoXiongSenLin, MapNameConstant.LingHunZhiSen, MapNameConstant.QingQuanBuBu } },
      { MapNameConstant.LingHunZhiSen, new List<string> { MapNameConstant.QingQuanHu, MapNameConstant.GaiYaCheng } },
      { MapNameConstant.QingQuanBuBu, new List<string> { MapNameConstant.QingQuanHu, MapNameConstant.LongHeHeGu } },
      { MapNameConstant.LongHeHeGu, new List<string> { MapNameConstant.QingQuanBuBu, MapNameConstant.LongJiShan } },
      { MapNameConstant.GaiYaCheng, new List<string> { MapNameConstant.LingHunZhiSen } },
    };

    [StaticField]
    public static Dictionary<string, string> specialMapPoint = new()
    {
      { MapNameConstant.LingHunCheng, "精灵城内小道" },
      { MapNameConstant.HanFengGu, "霜雪谷" },
    };

    [StaticField]
    public static Dictionary<string, KeyValuePair<string, string>> reviveMap = new()
    {
      { MapNameConstant.YouErYuan, new KeyValuePair<string, string>(MapNameConstant.YouErYuan, "医务室") },
      { MapNameConstant.MaoYinCun, new KeyValuePair<string, string>(MapNameConstant.MaoYinCun, "教堂") },
      { MapNameConstant.MaoYinXiCun, new KeyValuePair<string, string>(MapNameConstant.MaoYinCun, "教堂") },
      { MapNameConstant.MuYeCaoYuan, new KeyValuePair<string, string>(MapNameConstant.MaoYinCun, "教堂") },
      { MapNameConstant.WanMaCaoYuan, new KeyValuePair<string, string>(MapNameConstant.MaoYinCun, "教堂") },
      { MapNameConstant.DiDaiLinDi, new KeyValuePair<string, string>(MapNameConstant.TuoBaCheng, "教堂") },
      { MapNameConstant.TuoBaCheng, new KeyValuePair<string, string>(MapNameConstant.TuoBaCheng, "教堂") },
      { MapNameConstant.KeSiTePingYuan, new KeyValuePair<string, string>(MapNameConstant.TuoBaCheng, "教堂") },
      { MapNameConstant.WuYingDao, new KeyValuePair<string, string>(MapNameConstant.TuoBaCheng, "教堂") },
      { MapNameConstant.HanFengGu, new KeyValuePair<string, string>(MapNameConstant.BingFengCheng, "教堂") },
      { MapNameConstant.XueSongLinYuan, new KeyValuePair<string, string>(MapNameConstant.BingFengCheng, "教堂") },
      { MapNameConstant.DaXueYuan, new KeyValuePair<string, string>(MapNameConstant.BingFengCheng, "教堂") },
      { MapNameConstant.BingFengCheng, new KeyValuePair<string, string>(MapNameConstant.BingFengCheng, "教堂") },
      { MapNameConstant.BingHeLieGu, new KeyValuePair<string, string>(MapNameConstant.BingFengCheng, "教堂") },
      { MapNameConstant.BingFengBuBu, new KeyValuePair<string, string>(MapNameConstant.XueYuanCheng, "教堂") },
      { MapNameConstant.YinGuangSenLin, new KeyValuePair<string, string>(MapNameConstant.TuoBaCheng, "教堂") },
      { MapNameConstant.BingFengWan, new KeyValuePair<string, string>(MapNameConstant.XueYuanCheng, "教堂") },
      { MapNameConstant.YinXuePingYuan, new KeyValuePair<string, string>(MapNameConstant.XueYuanCheng, "教堂") },
      { MapNameConstant.XueYuanCheng, new KeyValuePair<string, string>(MapNameConstant.XueYuanCheng, "教堂") },
      { MapNameConstant.DongTuTaiYuan, new KeyValuePair<string, string>(MapNameConstant.XueYuanCheng, "教堂") },
      { MapNameConstant.MaWangTan, new KeyValuePair<string, string>(MapNameConstant.BaiMaGang, "教堂") },
      { MapNameConstant.LuWeiDang, new KeyValuePair<string, string>(MapNameConstant.BaiMaGang, "教堂") },
      { MapNameConstant.WuMingHu, new KeyValuePair<string, string>(MapNameConstant.BaiMaGang, "教堂") },
      { MapNameConstant.BaiMaGang, new KeyValuePair<string, string>(MapNameConstant.BaiMaGang, "教堂") },
      { MapNameConstant.BaiMaHeKou, new KeyValuePair<string, string>(MapNameConstant.BaiMaGang, "教堂") },
      { MapNameConstant.JinShaWan, new KeyValuePair<string, string>(MapNameConstant.XiSaiPingYuan, "教堂") },
      { MapNameConstant.XiSaiPingYuan, new KeyValuePair<string, string>(MapNameConstant.XiSaiPingYuan, "教堂") },
      { MapNameConstant.LongJiShan, new KeyValuePair<string, string>(MapNameConstant.XiSaiPingYuan, "教堂") },
      { MapNameConstant.LingHunShiJie, new KeyValuePair<string, string>(MapNameConstant.LingHunCheng, "教堂") },
      { MapNameConstant.LingHunCheng, new KeyValuePair<string, string>(MapNameConstant.LingHunCheng, "教堂") },
      { MapNameConstant.MaoXiongSenLin, new KeyValuePair<string, string>(MapNameConstant.QingQuanHu, "教堂") },
      { MapNameConstant.QingQuanHu, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "比武场") },
      { MapNameConstant.LingHunZhiSen, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "比武场") },
      { MapNameConstant.QingQuanBuBu, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "比武场") },
      { MapNameConstant.LongHeHeGu, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "比武场") },
      { MapNameConstant.GaiYaCheng, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "比武场") },
      { MapNameConstant.YinYueDiGuo, new KeyValuePair<string, string>(MapNameConstant.GaiYaCheng, "教堂") },
      { MapNameConstant.XiaoHeiWu, new KeyValuePair<string, string>(MapNameConstant.XiaoHeiWu, "小黑屋") },
    };

    public static KeyValuePair<string, string> getReviveMap(string mapName)
    {
      if (reviveMap.ContainsKey(mapName))
      {
        return reviveMap[mapName];
      }
      return new KeyValuePair<string, string>(MapNameConstant.MaoYinCun, "教堂");
    }
  }
}
