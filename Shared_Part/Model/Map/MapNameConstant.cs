using System.Collections.Generic;

namespace Mao<PERSON>ou<PERSON>i
{
  public static class MapNameConstant
  {
    [StaticField]
    public static List<string> AllMapName { get; } = new() {
      You<PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON>,
      KeSiTeP<PERSON>,
      Ma<PERSON>ang<PERSON>an,
      LuWeiDang,
      Tuo<PERSON>a<PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON>,
      BingFeng<PERSON>u<PERSON>u,
      BingHeLie<PERSON>u,
      <PERSON><PERSON><PERSON>,
      Wu<PERSON>,
      Bai<PERSON>,
      BaiMa<PERSON>,
      <PERSON><PERSON>,
      Xi<PERSON>ai<PERSON>,
      Ling<PERSON>un<PERSON>,
      <PERSON><PERSON>,
      MaoXiongSen<PERSON>in,
      Qing<PERSON>uan<PERSON>u,
      LingHun<PERSON>hi<PERSON>en,
      QingQuanBuBu,
      <PERSON><PERSON>e<PERSON>e<PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON>,
      <PERSON>oGuan,
      <PERSON><PERSON>hang,
    };

    public const string YouErYuan = "幼稚园";
    public const string MaoYinCun = "猫隐村";
    public const string MuYeCaoYuan = "牧野草原";
    public const string MaoYinXiCun = "猫隐西村";
    public const string WanMaCaoYuan = "万马草原";
    public const string XueSongLinYuan = "雪松森林";
    public const string DaXueYuan = "大雪山";
    public const string HanFengGu = "寒风谷";
    public const string BingFengCheng = "冰封城";
    public const string DiDaiLinDi = "低矮林地";
    public const string WuYingDao = "雾影岛";
    public const string KeSiTePingYuan = "卡斯特平原";
    public const string MaWangTan = "马王滩";
    public const string LuWeiDang = "芦苇荡";
    public const string TuoBaCheng = "拖把城";
    public const string YinGuangSenLin = "银光森林";
    public const string YinXuePingYuan = "银雪平原";
    public const string XueYuanCheng = "雪原城";
    public const string BingFengWan = "冰封湾";
    public const string BingFengBuBu = "冰封瀑布";
    public const string BingHeLieGu = "冰河裂谷";
    public const string DongTuTaiYuan = "冻土苔原";
    public const string WuMingHu = "无名湖";
    public const string BaiMaGang = "白马港";
    public const string BaiMaHeKou = "白马河口";
    public const string JinShaWan = "金沙湾";
    public const string XiSaiPingYuan = "西塞平原";
    public const string LingHunShiJie = "精灵世界";
    public const string LingHunCheng = "精灵城";
    public const string MaoXiongSenLin = "熊猫森林";
    public const string QingQuanHu = "清泉湖";
    public const string LingHunZhiSen = "精灵之森";
    public const string QingQuanBuBu = "清泉瀑布";
    public const string LongHeHeGu = "龙河河谷";
    public const string GaiYaZhiCheng = "盖亚之城";
    public const string YinYueDiGuo = "银月帝国";
    public const string LongJiShan = "龙脊山";
    public const string DaTaoShaDao = "大逃杀岛";
    public const string LianGongFang = "练功房";
    public const string GaoJiLianGongFang = "高级练功房";
    public const string LvGuan = "旅馆";
    public const string GaoJiLvGuan = "高级旅馆";
    public const string XiaoHeiWu = "小黑屋";
    public const string MoFaKongJian = "魔法空间";
    public const string ChanFang = "禅房";
    public const string DaoGuan = "道馆";
    public const string GongChang = "工厂";
  }
}