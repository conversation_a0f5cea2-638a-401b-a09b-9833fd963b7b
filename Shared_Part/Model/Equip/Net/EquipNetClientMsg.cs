using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEquipThingMsg)]
  public partial class EquipThingMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientUnEquipThingMsg)]
  public partial class UnEquipThingMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDrillEquipMsg)]
  public partial class DrillEquipMsg : MaoYouMessage, ILocationMessage
  {
    public long equipId;
    public long drillingItemId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientAddGemMsg)]
  public partial class AddGemMsg : MaoYouMessage, ILocationMessage
  {
    public bool isEquiped;
    public long gemId;
    public long equipId;
    public int gemSlot;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientRemoveGemMsg)]
  public partial class RemoveGemMsg : MaoYouMessage, ILocationMessage
  {
    public bool isEquiped;
    public long equipId;
    public int gemSlot;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEnhanceEquipMsg)]
  public partial class EnhanceEquipMsg : MaoYouMessage, ILocationMessage
  {
    public long equipId;
    public long shengJiShiId;
    public long wanBiId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientRepairEquipMsg)]
  public partial class RepairEquipMsg : MaoYouMessage, ILocationMessage
  {
    public long equipId;
    public bool isRepairAll = false;
  }
}