using System.Collections.Generic;

namespace MaoYouJi
{
  [EnableClass]
  public class EquipConstant
  {
    public static bool IsSpecialEquip(ThingSubType thingSubType)
    {
      return SpecialEquipList.Contains(thingSubType);
    }

    [StaticField]
    public static HashSet<ThingSubType> SpecialEquipList = new()
    {
      ThingSubType.Special_Equip_Recover,
      ThingSubType.Special_Equip_Recover_Blue,
      ThingSubType.Special_Equip_Hufu,
      ThingSubType.Special_Equip_Rode
    };

    [StaticField]
    public static readonly Dictionary<EquipPart, List<GemType>> EquipCanGems = new Dictionary<EquipPart, List<GemType>>
    {
      // 攻击类装备
      { EquipPart.Wu_Qi, new List<GemType> { GemType.Attack_Gem, GemType.Crit_Gem, GemType.Hit_Gem, GemType.Blue_Gem } }, // 武器：攻击、暴击、命中、蓝量
      { EquipPart.Xiang_Lian, new List<GemType> { GemType.Attack_Gem, GemType.Crit_Gem, GemType.Blue_Gem, GemType.Magic_Defense_Gem } }, // 项链：攻击、暴击、蓝量、魔防
      { EquipPart.Jie_Zhi, new List<GemType> { GemType.Attack_Gem, GemType.Crit_Gem, GemType.Blood_Gem, GemType.Hit_Gem } }, // 戒指：攻击、暴击、血量、命中

      // 防御类装备
      { EquipPart.Dun_Pai, new List<GemType> { GemType.Defense_Gem, GemType.Magic_Defense_Gem, GemType.Blood_Gem, GemType.Miss_Gem } }, // 盾牌：物防、魔防、血量、闪避
      { EquipPart.Xiong_Bu, new List<GemType> { GemType.Defense_Gem, GemType.Blood_Gem, GemType.Miss_Gem, GemType.Magic_Defense_Gem } }, // 胸部：物防、血量、闪避、魔防
      { EquipPart.Jian_Bu, new List<GemType> { GemType.Defense_Gem, GemType.Magic_Defense_Gem, GemType.Miss_Gem, GemType.Blood_Gem } }, // 肩部：物防、魔防、闪避、血量

      // 敏捷类装备
      { EquipPart.Shou_Tao, new List<GemType> { GemType.Hit_Gem, GemType.Miss_Gem, GemType.Defense_Gem, GemType.Attack_Gem } }, // 手套：命中、闪避、物防、攻击
      { EquipPart.Tui_Bu, new List<GemType> { GemType.Defense_Gem, GemType.Miss_Gem, GemType.Blood_Gem, GemType.Magic_Defense_Gem } }, // 腿部：物防、闪避、血量、魔防
      { EquipPart.Xie_Zi, new List<GemType> { GemType.Miss_Gem, GemType.Defense_Gem, GemType.Magic_Defense_Gem, GemType.Hit_Gem } }, // 鞋子：闪避、物防、魔防、命中
      { EquipPart.Wan_Bu, new List<GemType> { GemType.Hit_Gem, GemType.Defense_Gem, GemType.Miss_Gem, GemType.Blood_Gem } }, // 腕部：命中、物防、闪避、血量

      // 特殊部位
      { EquipPart.Tou_Bu, new List<GemType> { GemType.Blood_Gem, GemType.Blue_Gem, GemType.Magic_Defense_Gem, GemType.Crit_Gem } }, // 头部：血量、蓝量、魔防、暴击
      { EquipPart.Yao_Bu, new List<GemType> { GemType.Blood_Gem, GemType.Defense_Gem, GemType.Magic_Defense_Gem, GemType.Miss_Gem } }, // 腰部：血量、物防、魔防、闪避
    };

    [StaticField]
    public static readonly Dictionary<MaoJob, List<ThingNameEnum>> JobHufuMap = new Dictionary<MaoJob, List<ThingNameEnum>>
    {
      { MaoJob.AnYin_CiKe, new List<ThingNameEnum> { ThingNameEnum.AnShaZhe_HuFu, ThingNameEnum.GaoJi_AnShaZhe_HuFu, ThingNameEnum.QiangXiao_AnShaZhe_HuFu } },
      { MaoJob.QianKun_QiangShou, new List<ThingNameEnum> { ThingNameEnum.QiangShen_HuFu, ThingNameEnum.GaoJi_QiangShen_HuFu, ThingNameEnum.QiangXiao_QiangShen_HuFu } },
      { MaoJob.HuangJia_WeiShi, new List<ThingNameEnum> { ThingNameEnum.JianShen_HuFu, ThingNameEnum.GaoJi_JianShen_HuFu, ThingNameEnum.QiangXiao_JianShen_HuFu } },
      { MaoJob.JiFeng_LangKe, new List<ThingNameEnum> { ThingNameEnum.DaoMo_HuFu, ThingNameEnum.GaoJi_DaoMo_HuFu, ThingNameEnum.QiangXiao_DaoMo_HuFu } },
      { MaoJob.NuLing_WuZhe, new List<ThingNameEnum> { ThingNameEnum.LanJieZhe_HuFu, ThingNameEnum.GaoJi_LanJieZhe_HuFu, ThingNameEnum.QiangXiao_LanJieZhe_HuFu } },
      { MaoJob.Yan_ShuShi, new List<ThingNameEnum> { ThingNameEnum.HuoYan_HuFu, ThingNameEnum.GaoJi_HuoYan_HuFu, ThingNameEnum.QiangXiao_HuoYan_HuFu } },
      { MaoJob.Bin_ShuShi, new List<ThingNameEnum> { ThingNameEnum.HanBing_HuFu, ThingNameEnum.GaoJi_HanBing_HuFu, ThingNameEnum.QiangXiao_HanBing_HuFu } },
      { MaoJob.Feng_ShuShi, new List<ThingNameEnum> { ThingNameEnum.FengYun_HuFu, ThingNameEnum.GaoJi_FengYun_HuFu, ThingNameEnum.QiangXiao_FengYun_HuFu } },
      { MaoJob.Tian_DaoShi, new List<ThingNameEnum> { ThingNameEnum.ShengGuang_HuFu, ThingNameEnum.GaoJi_ShengGuang_HuFu, ThingNameEnum.QiangXiao_ShengGuang_HuFu } },
      { MaoJob.Tian_MinShi, new List<ThingNameEnum> { ThingNameEnum.AnYin_HuFu, ThingNameEnum.GaoJi_AnYin_HuFu, ThingNameEnum.QiangXiao_AnYin_HuFu } },
    };
  }
}