using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [ComponentOf(typeof(User))]
  public class VipAfkComp : Entity, IAwake, ISerializeToEntity
  {
    public long enterTime { get; set; } = 0; // 进入挂机房间时间
    public long offlineTime { get; set; } = 0; // 离线时间
    public bool isCalced { get; set; } = false; // 是否计算过
    public bool isGaoji { get; set; } = false; // 是否是高级挂机房间
    public long setTime { get; set; } = 0; // 设置的挂机时间，单位分钟
    public long remainTime { get; set; } = 0; // 挂机房间剩余时间，单位分钟
    public long totalExp { get; set; } = 0; // 挂机房间总经验
    public string preMap { get; set; } // 挂机前所在地图
    public string prePoint { get; set; } // 挂机前所在地图的点

    public AfkSystemType afkSystemType { get; set; } // 挂机类型
    public long existTime { get; set; } = 0; // 旅馆经验加成持续时间
    public long addZenExp { get; set; } = 0; // 旅馆增加禅的经验值

    public SkillIdEnum skillId { get; set; } // 练功时使用的技能
  }
}