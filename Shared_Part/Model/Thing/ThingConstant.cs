using System.Collections.Generic;

namespace MaoYouJi
{
  [EnableClass]
  public static class ThingConstant
  {
    public static bool IsSpecialQuickThing(ThingNameEnum thingName)
    {
      return specialQuickThings.Contains(thingName);
    }

    [StaticField]
    private static readonly HashSet<ThingNameEnum> specialQuickThings = new()
    {
      ThingNameEnum.<PERSON><PERSON><PERSON>_<PERSON>,
      ThingNameEnum.<PERSON><PERSON><PERSON>_<PERSON>,
      ThingNameEnum.Zhi_FengZheng
    };
  }
}