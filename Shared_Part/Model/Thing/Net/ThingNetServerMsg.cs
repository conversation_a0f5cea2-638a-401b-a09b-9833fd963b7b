using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateBagMsg)]
  public partial class ServerUpdateBagMsg : MaoYouMessage, ILocationMessage
  {
    public bool isAll = false;
    public bool isCoin = false;
    public long coinNum = 0, catBeanNum = 0, catEyeNum = 0;
    public int packCapNum = 0;
    public List<Thing> updateList = new();
    public List<long> removeThingIds = new();
    public BagDaoInfo bag;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerUpdateAccountBagMsg)]
  public partial class ServerUpdateAccountBagMsg : MaoYouMessage, ILocationMessage
  {
    public List<Thing> updateList = new();
    public List<long> removeThingIds = new();
  }
}