using System.Collections.Generic;
using MemoryPack;

namespace MaoYouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientSavePointMsg)]
  public partial class ClientSavePointMsg : MaoYouMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDelSavePointMsg)]
  public partial class ClientDelSavePointMsg : MaoYouMessage, ILocationMessage
  {
    public bool isOut = false;
    public string delMap = "";
    public string delPoint = "";
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientQuickFoodMsg)]
  public partial class ClientQuickFoodMsg : MaoYouMessage, ILocationMessage
  {
    public Dictionary<long, int> quickFoods = new(); // 物品ID -> 快捷栏位置
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientUseThingMsg)]
  public partial class ClientUseThingMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId = 0;
    public int thingNum = 1; // 使用数量，默认为1
    public SkillIdEnum skillId;
    public string targetUserId; // 目标用户id或名称
    public string msg; // 大喇叭发言内容
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDropThingMsg)]
  public partial class ClientDropThingMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId = 0;
    public int num;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientSellThingMsg)]
  public partial class ClientSellThingMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId = 0;
    public int num;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientSaveToAccountBagMsg)]
  public partial class ClientSaveToAccountBagMsg : MaoYouMessage, ILocationMessage
  {
    public List<long> thingIds;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetFromAccountBagMsg)]
  public partial class ClientGetFromAccountBagMsg : MaoYouMessage, ILocationMessage
  {
    public long thingId;
    public int num;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientAddBagCapacityMsg)]
  public partial class ClientAddBagCapacityMsg : MaoYouMessage, ILocationMessage
  {
    public int costCatBean;
    // 0 为角色背包，1为账户仓库
    public int addType;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientPutInPackMsg)]
  public partial class ClientPutInPackMsg : MaoYouMessage, ILocationMessage
  {
    public long bagPackId;
    public long thingId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientGetFromPackMsg)]
  public partial class ClientGetFromPackMsg : MaoYouMessage, ILocationMessage
  {
    public long bagPackId;
    public bool isAll = false;
    public long thingId;
  }
}