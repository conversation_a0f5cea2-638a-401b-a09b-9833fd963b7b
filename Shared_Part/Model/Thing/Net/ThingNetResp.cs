using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowBagResp)]
  public partial class ShowBagResp : MaoYouOutMessage, ILocationResponse
  {
    public BagDaoInfo bagDaoInfo;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ReOrderBagResp)]
  public partial class ReOrderBagResp : MaoYouOutMessage, ILocationResponse
  {
    public bool needNewData = false; // 是否需要新的数据，不需要的话本地执行重排逻辑
    public BagDaoInfo bagDaoInfo; // 新的背包数据
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowAccountBagResp)]
  public partial class ShowAccountBagResp : MaoYouOutMessage, ILocationResponse
  {
    public BagDaoInfo bagDaoInfo;
  }
}