using System.Collections.Generic;
using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowBagReq)]
  [ResponseType(nameof(ShowBagResp))]
  public partial class ShowBagReq : MaoYouInMessage, ILocationRequest
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ReOrderBagReq)]
  [ResponseType(nameof(ReOrderBagResp))]
  public partial class ReOrderBagReq : MaoYouInMessage, ILocationRequest
  {
    // 发送当前的物品ID列表，如果和服务器端的一模一样，那就在本地执行重排
    public List<long> thingIds;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ShowAccountBagReq)]
  [ResponseType(nameof(ShowAccountBagResp))]
  public partial class ShowAccountBagReq : MaoYouInMessage, ILocationRequest
  {
  }
}