namespace MaoYouJi
{
  // 物品消息 45001 - 47000
  public partial class MaoOuterMessageRange
  {
    public const ushort ShowBagReq = 45001; // 展示背包请求
    public const ushort ShowBagResp = 45002; // 展示背包响应
    public const ushort ServerUpdateBagMsg = 45003; // 更新背包详情
    public const ushort ReOrderBagReq = 45004; // 重新排序背包请求
    public const ushort ReOrderBagResp = 45005; // 重新排序背包响应
    public const ushort ClientSavePointMsg = 45006; // 保存点
    public const ushort ClientDelSavePointMsg = 45007; // 删除保存点
    public const ushort ClientQuickFoodMsg = 45008; // 快捷食物
    public const ushort ClientUseThingMsg = 45009; // 使用物品请求
    public const ushort ClientDropThingMsg = 45010; // 丢弃物品请求
    public const ushort ClientSellThingMsg = 45011; // 出售物品请求
    public const ushort ClientSaveToAccountBagMsg = 45012; // 保存到公共背包请求
    public const ushort ClientGetFromAccountBagMsg = 45013; // 从公共背包获取请求
    public const ushort ShowAccountBagReq = 45014; // 展示公共背包请求
    public const ushort ShowAccountBagResp = 45015; // 展示公共背包响应
    public const ushort ServerUpdateAccountBagMsg = 45016; // 更新公共背包详情
    public const ushort ClientAddBagCapacityMsg = 45017; // 增加背包容量请求
    public const ushort ClientPutInPackMsg = 45018; // 放入便携背包请求
    public const ushort ClientGetFromPackMsg = 45019; // 从便携背包获取请求
  }
}