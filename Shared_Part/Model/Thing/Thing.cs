using System;
using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  [EnableClass]
  [MemoryPackable]
  public partial class ThingGiveInfo : ICloneable
  {
    [MemoryPackConstructor]
    public ThingGiveInfo() { }

    public ThingGiveInfo(ThingNameEnum thingName, OwnType ownType, int num, BaseJob job = BaseJob.None, int prob = 10000)
    {
      this.thingName = thingName;
      this.ownType = ownType;
      this.num = num;
      this.job = job;
      this.prob = prob;
    }

    public ThingNameEnum thingName;
    public OwnType ownType;
    public int num;
    public BaseJob job; // 职业，默认为空不限制
    public int prob = 10000; // 概率，万分比

    public object Clone()
    {
      ThingGiveInfo thingGiveInfo = (ThingGiveInfo)this.MemberwiseClone();
      return thingGiveInfo;
    }
  }

  [EnableClass]
  [MemoryPackable]
  [MemoryPackUnion(0, typeof(Food))]
  [MemoryPackUnion(1, typeof(Material))]
  [MemoryPackUnion(2, typeof(TaskThing))]
  [MemoryPackUnion(3, typeof(Treasure))]
  [MemoryPackUnion(4, typeof(Equipment))]
  [MemoryPackUnion(5, typeof(Mine))]
  [MemoryPackUnion(6, typeof(RealRode))]
  public abstract partial class Thing : ICloneable
  {
    public ObjectId id; // 唯一ID
    public long thingId; // 物品ID，背包中的物品必须赋予
    public ThingNameEnum thingName;
    public string name; // 物品名称
    public ThingType thingType; // 物品类型
    [MemoryPackIgnore]
    public ThingSubType thingSubType = ThingSubType.None; // 物品子类型
    [MemoryPackIgnore]
    public int stackNum = 1; // 可堆叠数量
    public int num = 1; // 当前数量
    [MemoryPackIgnore]
    public int salePrice; // 出售价格，为空代表禁止出售
    [MemoryPackIgnore]
    public long optFlags; // 操作标记
    public ThingGrade grade; // 品级
    public OwnType ownType = OwnType.PUBLIC; // 拥有类型
    [MemoryPackIgnore]
    public long cd; // 冷却时间
    [MemoryPackIgnore]
    public long coolTime; // 结束冷却的时间点
    [MemoryPackIgnore]
    public long minLevel; // 最小使用等级
    [MemoryPackIgnore]
    public long maxLevel; // 最大使用等级
    public long val; // 属性数值，不同类型的物品有不同的含义
    public long[] vals; // 属性数值列表，不同类型的物品有不同的含义
    public bool enabled = false; // 是否启用，默认为空，只有部分特殊道具需要启用，如寻路罗盘

    public bool canDrop()
    {
      return (optFlags & 1) == 1;
    }

    public bool canSell()
    {
      return (optFlags & 2) == 2;
    }

    public bool canUse()
    {
      return (optFlags & 4) == 4;
    }

    public bool canEquip()
    {
      return (optFlags & 8) == 8;
    }

    public bool canUseInFight()
    {
      return (optFlags & 16) == 16;
    }

    public bool canEnable()
    {
      return (optFlags & 64) == 64;
    }

    public bool canUseWhileDead()
    {
      return (optFlags & 128) == 128;
    }

    public virtual object Clone()
    {
      Thing thing = (Thing)this.MemberwiseClone();
      if (this.vals != null)
      {
        thing.vals = (long[])this.vals.Clone();
      }
      return thing;
    }
  }
}